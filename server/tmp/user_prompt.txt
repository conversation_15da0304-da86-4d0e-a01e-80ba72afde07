
        Here is the transcripts:
        [
  {
    "user_id": 299018,
    "timestamp": "00:00-00:01",
    "user_name": "<PERSON> Rakamin",
    "transcript": "<PERSON><PERSON>,"
  },
  {
    "user_id": 299018,
    "timestamp": "00:01-00:08",
    "user_name": "<PERSON> Rakamin",
    "transcript": "selamat siang semua. Terima kasih ya sudah hadir eh untuk mengikuti kegiatan siang hari ini."
  },
  {
    "user_id": 299018,
    "timestamp": "00:08-00:09",
    "user_name": "<PERSON> Rakamin",
    "transcript": "<PERSON><PERSON>,"
  },
  {
    "user_id": 299018,
    "timestamp": "00:09-00:16",
    "user_name": "<PERSON> Rakamin",
    "transcript": "eh kegiatan hari ini tuh ngapain sih? Mungkin diriku eh share sedikit ya,"
  },
  {
    "user_id": 299018,
    "timestamp": "00:16-00:20",
    "user_name": "Assessment Rakamin",
    "transcript": "terkait sama apa yang akan kita lakukan mungkin satu jam ke depan."
  },
  {
    "user_id": 299018,
    "timestamp": "00:20-00:23",
    "user_name": "Assessment Rakamin",
    "transcript": "kita lakukan mungkin 1 jam ke depan."
  },
  {
    "user_id": 299018,
    "timestamp": "00:23-00:25",
    "user_name": "Assessment Rakamin",
    "transcript": "Seperti itu. Sebentar."
  },
  {
    "user_id": 299018,
    "timestamp": "00:26-00:27",
    "user_name": "Assessment Rakamin",
    "transcript": "Oh oke."
  },
  {
    "user_id": 299018,
    "timestamp": "00:27-00:30",
    "user_name": "Assessment Rakamin",
    "transcript": "Sekarang udah jelaskah suaranya?"
  },
  {
    "user_id": 299018,
    "timestamp": "00:31-00:32",
    "user_name": "Assessment Rakamin",
    "transcript": "Amankah?"
  },
  {
    "user_id": 299018,
    "timestamp": "00:33-00:34",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke."
  },
  {
    "user_id": 299018,
    "timestamp": "00:34-00:46",
    "user_name": "Assessment Rakamin",
    "transcript": "Ya, jadi eh mungkin dari teman-teman juga udah dapat info ya dari tim HC terkait dengan satu minggu ke depan kita akan melakukan"
  },
  {
    "user_id": 299018,
    "timestamp": "00:40-00:49",
    "user_name": "Assessment Rakamin",
    "transcript": "eh mungkin dari teman-teman juga udah dapat info ya dari tim HC terkait dengan satu minggu ke depan kita akan melakukan eh rangkaian asesmen."
  },
  {
    "user_id": 299018,
    "timestamp": "00:49-00:54",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, eh yang kita lakukan siang hari ini itu merupakan salah satunya. Jadi kita akan melakukan eh LGD gitu atau Assessment Rakamin group discussion."
  },
  {
    "user_id": 299018,
    "timestamp": "00:54-01:02",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, tujuannya apa sih kita melakukan eh kegiatan siang hari ini? Jadi diharapkan eh ini dapat melihat"
  },
  {
    "user_id": 299018,
    "timestamp": "01:00-01:02",
    "user_name": "Assessment Rakamin",
    "transcript": "group discussion."
  },
  {
    "user_id": 299018,
    "timestamp": "01:02-01:07",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, tujuannya apa sih kita melakukan eh kegiatan siang hari ini?"
  },
  {
    "user_id": 299018,
    "timestamp": "01:07-01:17",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi diharapkan eh ini dapat melihat bagaimana teman-teman berinteraksi dalam kelompok, bagaimana menyampaikan ide, mendengarkan pendapat orang lain dan mencapai kesepakatan kesepakatan bersama."
  },
  {
    "user_id": 299018,
    "timestamp": "01:17-01:19",
    "user_name": "Assessment Rakamin",
    "transcript": "Seperti itu."
  },
  {
    "user_id": 299018,
    "timestamp": "01:19-01:30",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, instruksinya, jadi nanti eh setelah eh tidak ada pertanyaan, diriku akan memberikan link eh menu link yang eh menuju satu kasus gitu ya."
  },
  {
    "user_id": 299018,
    "timestamp": "01:30-01:37",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi nanti eh teman-teman diharapkan untuk dapat eh membaca, eh menganalisa kasus tersebut selama 15 menit."
  },
  {
    "user_id": 299018,
    "timestamp": "01:37-01:43",
    "user_name": "Assessment Rakamin",
    "transcript": "Tapi diharapkan untuk tidak men-download apalagi menyebarluaskan eh kasus yang ada."
  },
  {
    "user_id": 299018,
    "timestamp": "01:43-01:45",
    "user_name": "Assessment Rakamin",
    "transcript": "Lalu setelah 15 menit selesai, kita akan hm."
  },
  {
    "user_id": 299018,
    "timestamp": "01:45-01:53",
    "user_name": "Assessment Rakamin",
    "transcript": "Lalu setelah 15 menit selesai, kita akan teman-teman akan melakukan diskusi selama 30 sampai 45 menit."
  },
  {
    "user_id": 299018,
    "timestamp": "01:53-02:03",
    "user_name": "Assessment Rakamin",
    "transcript": "Lalu eh di akhir waktu eh akan ada satu pes- satu peserta gitu ya, satu orang yang eh merangkum apa saja yang eh hasil dari diskusinya."
  },
  {
    "user_id": 299018,
    "timestamp": "02:03-02:07",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, selama diskusi berlangsung, tidak ada pemimpin yang ditunjuk."
  },
  {
    "user_id": 299018,
    "timestamp": "02:07-02:16",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, selama diskusi berlangsung, tidak ada pemimpin yang ditunjuk. Jadi eh teman-teman di sini semua sama, eh tidak ada yang eh dianggap kedudukannya lebih tinggi gitu ya."
  },
  {
    "user_id": 299018,
    "timestamp": "02:16-02:29",
    "user_name": "Assessment Rakamin",
    "transcript": "Lalu eh teman-teman diharapkan juga dapat berpartisipasi aktif menyampaikan pendapatnya, eh lalu memberikan masukan dan mencari solusi bersama dari masalah yang ada."
  },
  {
    "user_id": 299018,
    "timestamp": "02:29-02:35",
    "user_name": "Assessment Rakamin",
    "transcript": "Mungkin untuk tambahan, eh di kasus tersebut tidak ada jawaban benar atau salah."
  },
  {
    "user_id": 299018,
    "timestamp": "02:35-02:44",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi eh apapun pendapat yang teman-teman sampaikan, eh itu tidak ada jawaban eh mana yang benar, mana yang salah."
  },
  {
    "user_id": 299018,
    "timestamp": "02:44-02:46",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi eh fokus saja bagaimana teman-teman berdiskusi untuk menyelesaikan masalah yang ada."
  },
  {
    "user_id": 299018,
    "timestamp": "02:46-02:47",
    "user_name": "Assessment Rakamin",
    "transcript": "Seperti itu."
  },
  {
    "user_id": 299018,
    "timestamp": "02:47-02:49",
    "user_name": "Assessment Rakamin",
    "transcript": "Apakah ada yang mau ditanya terlebih"
  },
  {
    "user_id": 299018,
    "timestamp": "02:49-02:52",
    "user_name": "Assessment Rakamin",
    "transcript": "dahulu?"
  },
  {
    "user_id": 299018,
    "timestamp": "02:52-02:53",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke, mungkin untuk tambahan, jadi nanti selama proses berlangsung diharapkan untuk eh mic-nya dibuka aja."
  },
  {
    "user_id": 299017,
    "timestamp": "02:53-02:54",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Jelas."
  },
  {
    "user_id": 299018,
    "timestamp": "02:54-03:07",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi enggak usah eh di-mute karena eh takutnya nanti ada pendapat yang eh mau disampaikan gitu."
  },
  {
    "user_id": 299018,
    "timestamp": "03:07-03:13",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi enggak apa-apa eh mic-nya dibuka aja dan kameranya juga dinyalakan aja enggak apa-apa."
  },
  {
    "user_id": 299018,
    "timestamp": "03:13-03:17",
    "user_name": "Assessment Rakamin",
    "transcript": "Nah, selama proses LGD ini berlangsung, jadi, Kak, nanti akan didampingi oleh Awang ya."
  },
  {
    "user_id": 299018,
    "timestamp": "03:17-03:23",
    "user_name": "Assessment Rakamin",
    "transcript": "Seperti itu. Jadi eh selama teman-teman diskusi, abaikan kita berdua enggak apa-apa."
  },
  {
    "user_id": 299018,
    "timestamp": "03:23-03:25",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke, ada yang mau ditanyakan?"
  },
  {
    "user_id": 299017,
    "timestamp": "03:26-03:27",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Tidak."
  },
  {
    "user_id": 299021,
    "timestamp": "03:27-03:28",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Tidak."
  },
  {
    "user_id": 299018,
    "timestamp": "03:28-03:38",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke. Kalau tidak ada, mungkin diriku share ya untuk link kasusnya. Nanti eh teman-teman boleh juga untuk eh mencatat di Google eh di Google Docs atau di catatannya masing-masing. Seperti itu."
  },
  {
    "user_id": 299018,
    "timestamp": "03:39-03:42",
    "user_name": "Assessment Rakamin",
    "transcript": "Link-nya sudah diriku share di inbox, apakah bisa"
  },
  {
    "user_id": 299018,
    "timestamp": "03:42-03:47",
    "user_name": "Assessment Rakamin",
    "transcript": "dibuka?"
  },
  {
    "user_id": 299017,
    "timestamp": "03:48-03:50",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Bisa Mbak Nada."
  },
  {
    "user_id": 299018,
    "timestamp": "03:50-03:52",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke. Iya."
  },
  {
    "user_id": 299018,
    "timestamp": "03:54-03:56",
    "user_name": "Assessment Rakamin",
    "transcript": "Semua sudah membuka kasusnya?"
  },
  {
    "user_id": 299020,
    "timestamp": "03:57-03:58",
    "user_name": "Putri Rosalinda",
    "transcript": "Sudah."
  },
  {
    "user_id": 299017,
    "timestamp": "03:58-03:59",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Sudah."
  },
  {
    "user_id": 299018,
    "timestamp": "03:59-04:04",
    "user_name": "Assessment Rakamin",
    "transcript": "Eh Mbak Putri, Ulum, Mbak Salma mungkin boleh di-open mic aja enggak apa-apa."
  },
  {
    "user_id": 299018,
    "timestamp": "04:04-04:10",
    "user_name": "Assessment Rakamin",
    "transcript": "Iya, jadi dariku takutnya eh enggak ngeh gitu ya, siapa tahu ada pertanyaan gitu."
  },
  {
    "user_id": 299020,
    "timestamp": "04:10-04:13",
    "user_name": "Putri Rosalinda",
    "transcript": "Aku aman nih sudah kebuka. Eh tes tadi internal rakamin."
  },
  {
    "user_id": 299018,
    "timestamp": "04:13-04:14",
    "user_name": "Assessment Rakamin",
    "transcript": "Iya, betul."
  },
  {
    "user_id": 299018,
    "timestamp": "04:14-04:15",
    "user_name": "Assessment Rakamin",
    "transcript": "Mbak Salma juga udah kebuka ya?"
  },
  {
    "user_id": 299022,
    "timestamp": "04:15-04:16",
    "user_name": "Siti Salma Aulia",
    "transcript": "Udah."
  },
  {
    "user_id": 299018,
    "timestamp": "04:16-04:42",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke, kalau sudah mungkin eh sekarang teman-teman bisa mulai untuk membaca kasusnya selama 15 menit, tapi kalau memang jika sudah semua selesai sebelum waktunya eh diskusi bisa dilakukan lebih awal, seperti itu."
  },
  {
    "user_id": 299018,
    "timestamp": "20:12-20:25",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke, waktu untuk membaca kasusnya sudah selesai. Mungkin dari teman-teman boleh eh memulai diskusinya tapi mungkin sebelum mulai bisa diperkenalkan dulu ya, nama dan mungkin eh saat ini role-nya sebagai apa."
  },
  {
    "user_id": 299018,
    "timestamp": "20:25-20:26",
    "user_name": "Assessment Rakamin",
    "transcript": "Terima kasih."
  },
  {
    "user_id": 299017,
    "timestamp": "20:28-20:30",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Oke. Mau siapa duluan?"
  },
  {
    "user_id": 299022,
    "timestamp": "20:30-20:31",
    "user_name": "Siti Salma Aulia",
    "transcript": "Boleh dari Mbak Ranti dulu."
  },
  {
    "user_id": 299017,
    "timestamp": "20:31-20:48",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Oke, perkenalkan nama aku Ranti, posisi sekarang aku jadi HR di Rekamin. Salam kenal semuanya."
  },
  {
    "user_id": 299022,
    "timestamp": "20:37-20:38",
    "user_name": "Siti Salma Aulia",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299019,
    "timestamp": "20:39-20:44",
    "user_name": "Imelda",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299021,
    "timestamp": "20:45-20:46",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "20:46-20:47",
    "user_name": "Siti Salma Aulia",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "20:47-20:52",
    "user_name": "Siti Salma Aulia",
    "transcript": "Mungkin boleh dilanjut ke Mbak Putri?"
  },
  {
    "user_id": 299020,
    "timestamp": "20:57-21:06",
    "user_name": "Putri Rosalinda",
    "transcript": "Halo semua. Perkenalkan, eh, aku Putri Rosalinda dari Rakamin dan posisi sekarang sebagai rekrutmen konsultan. Salam kenal semua."
  },
  {
    "user_id": 299021,
    "timestamp": "20:57-20:58",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299017,
    "timestamp": "20:58-20:59",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "20:59-21:01",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, bisa lanjut ke Imel?"
  },
  {
    "user_id": 299019,
    "timestamp": "21:04-21:05",
    "user_name": "Imelda",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299021,
    "timestamp": "21:05-21:06",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "21:06-21:08",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, bisa lanjut ke Imel?"
  },
  {
    "user_id": 299019,
    "timestamp": "21:08-21:15",
    "user_name": "Imelda",
    "transcript": "Oke. Halo semuanya. Perkenalkan aku Imelda Putri Azara, rolku sebagai customer success officer di Rekamin. Salam kenal, guys."
  },
  {
    "user_id": 299021,
    "timestamp": "21:15-21:16",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299020,
    "timestamp": "21:16-21:17",
    "user_name": "Putri Rosalinda",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "21:17-21:19",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, bisa lanjut ke Mbak Ulum?"
  },
  {
    "user_id": 299021,
    "timestamp": "21:19-21:25",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Oke, halo. Perkenalkan semuanya, aku Ulum. Sekarang rolenya sebagai assessment psychometric."
  },
  {
    "user_id": 299021,
    "timestamp": "21:25-21:31",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Officer di Rakamin."
  },
  {
    "user_id": 299022,
    "timestamp": "21:31-21:32",
    "user_name": "Siti Salma Aulia",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299021,
    "timestamp": "21:32-21:33",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "21:33-21:41",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, thank you. Berarti bisa ditutup dari aku ya untuk perkenalan ini. Perkenalkan namaku Salma. Sekarang role-nya itu di product manager di perusahaan Rakamin."
  },
  {
    "user_id": 299022,
    "timestamp": "21:42-21:53",
    "user_name": "Siti Salma Aulia",
    "transcript": "Jadi terima kasih semuanya telah hadir di meeting hari ini."
  },
  {
    "user_id": 299022,
    "timestamp": "21:53-21:59",
    "user_name": "Siti Salma Aulia",
    "transcript": "Seperti yang kita sudah baca bersama, saat ini perusahaan kita sedang"
  },
  {
    "user_id": 299022,
    "timestamp": "21:59-22:00",
    "user_name": "Siti Salma Aulia",
    "transcript": "menghadapi tantangan serius."
  },
  {
    "user_id": 299018,
    "timestamp": "22:00-22:01",
    "user_name": "Assessment Rakamin",
    "transcript": "tantangan serius."
  },
  {
    "user_id": 299018,
    "timestamp": "22:01-22:08",
    "user_name": "Assessment Rakamin",
    "transcript": "Jadi dari case yang tadi, banyak nih karyawan kita yang ternyata mengalami burnout."
  },
  {
    "user_id": 299022,
    "timestamp": "22:08-22:09",
    "user_name": "Siti Salma Aulia",
    "transcript": "Nah, dan"
  },
  {
    "user_id": 299022,
    "timestamp": "22:09-22:17",
    "user_name": "Siti Salma Aulia",
    "transcript": "dan ternyata ini juga mulai berdampak pada kualitas pekerjaan juga serta kepuasan para stakeholder."
  },
  {
    "user_id": 299022,
    "timestamp": "22:17-22:25",
    "user_name": "Siti Salma Aulia",
    "transcript": "Jadi sebelum kita masuk ke solusi, kita coba dulu yuk, gali dulu nih akar masalahnya tuh apa aja?"
  },
  {
    "user_id": 299022,
    "timestamp": "22:25-22:27",
    "user_name": "Siti Salma Aulia",
    "transcript": "Jadi mungkin boleh di"
  },
  {
    "user_id": 299022,
    "timestamp": "22:27-22:33",
    "user_name": "Siti Salma Aulia",
    "transcript": "Jadi mungkin boleh dimulai dari ee Mbak Ulum dulu boleh?"
  },
  {
    "user_id": 299021,
    "timestamp": "22:33-22:34",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Oke."
  },
  {
    "user_id": 299021,
    "timestamp": "22:34-22:42",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Jadi setelah dibaca kasusnya Mbak Salma dan teman-teman semuanya, beberapa yang aku dapat itu masalahnya yang pertama karena ee jumlah project-nya itu mulai meningkat ya."
  },
  {
    "user_id": 299021,
    "timestamp": "22:42-22:46",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "jumlah projectnya meningkat tapi tidak dibarengi sama jumlah sumber daya manusia yang eh setara gitu."
  },
  {
    "user_id": 299021,
    "timestamp": "22:46-22:56",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Jadi jumlah sumber daya manusianya sedikit tapi eh jumlah projectnya meningkat dan skalanya besar gitu. Dan ketika jumlah projectnya itu banyak, maka"
  },
  {
    "user_id": 299021,
    "timestamp": "22:56-23:06",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Dan ketika jumlah projectnya itu banyak, maka kan eh timeline yang tumpang tindih itu tadi akan terjadi ya antar project. Nah, karena sumber dayanya kurang, jadinya kan em untuk ngebagi pikiran dari eh teman-teman karyawan ini kan eh ini ya, apa namanya? bikin burnout gitu untuk mereka."
  },
  {
    "user_id": 299021,
    "timestamp": "23:06-23:09",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Nah, selain itu juga eh bikin work time eh mereka juga nambah gitu."
  },
  {
    "user_id": 299021,
    "timestamp": "23:09-23:12",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Jadinya itu pekerjaannya juga overload gitu ya."
  },
  {
    "user_id": 299021,
    "timestamp": "23:12-23:17",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Terus juga eh apa namanya? dari mereka itu banyak merasa eh lelah, beban kerjanya berle-"
  },
  {
    "user_id": 299021,
    "timestamp": "23:17-23:25",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "dan eh ya itu tadi ya meningkatkan risiko untuk eh burnout tadi gitu. Mungkin dari teman-teman yang lain ada yang mau menambahkan?"
  },
  {
    "user_id": 299017,
    "timestamp": "23:25-23:30",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Mungkin aku mau nambahin ya terkait yang eh resource yang aku baca. Aku juga ngelihat eh jadi eh kebutuhan klien ke-"
  },
  {
    "user_id": 299017,
    "timestamp": "23:30-23:34",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "mungkin aku mau nambahin ya terkait yang root cause yang aku baca."
  },
  {
    "user_id": 299017,
    "timestamp": "23:34-23:35",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Aku juga ngelihat,"
  },
  {
    "user_id": 299017,
    "timestamp": "23:35-23:50",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "eh jadi eh kebutuhan klien kan kadang suka ada BM mendadak ya gitu, yang di mana mungkin eh itu masih adanya kurang kesepakatan atau agreement antara si perusahaan kita sama perusahaan klien tuh belum terlalu kuat, gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "23:50-23:53",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Jadinya kan kita juga sebagai penyedia jasa enggak bisa dong nolak gitu, tapi sedangkan kondisi di kita juga"
  },
  {
    "user_id": 299017,
    "timestamp": "23:53-23:58",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Jadinya kan kita juga sebagai penyedia jasa enggak bisa dong nolak gitu, tapi sedangkan kondisi di kita juga kurang memungkiri gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "23:58-24:03",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Jadi yang aku tangkap, belum ada agreement yang kuat sih, gitu, antara dua belah pihak."
  },
  {
    "user_id": 299017,
    "timestamp": "24:03-24:08",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Jadinya itu bisa berpotensi untuk memberatkan ke pihak kita juga, gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "24:08-24:20",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Terus, eh, aku juga ngebaca bahwa kita tuh masih sistemnya masih serba manual, gitu. Di mana kalau misalkan serba manual itu, jadinya akan memperhambat proses pengerjaan yang harusnya mungkin eh sejam udah beres gitu, tapi kalau karena ini sistemnya manual, input-input data, ngetik segala macamnya manual, jadi itu bisa ngambil 2 sampai 4 jam."
  },
  {
    "user_id": 299017,
    "timestamp": "24:20-24:26",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Itu bisa mempengaruhi dari segi mentalnya teman-teman gitu ya yang ngerjain karena pasti capek banget,"
  },
  {
    "user_id": 299017,
    "timestamp": "24:26-24:26",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "dan"
  },
  {
    "user_id": 299017,
    "timestamp": "24:26-24:32",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Itu bisa mempengaruhi dari segi mentalnya teman-teman gitu ya yang ngerjain karena pasti capek banget dan juga fisiknya juga karena buang waktu banget."
  },
  {
    "user_id": 299017,
    "timestamp": "24:32-24:33",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "24:33-24:40",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "sama yang aku tangkap itu belum ada kejelasan terkait dengan SOP lembur gitu yang di mana seharusnya setiap divisi itu memiliki batas maksimal lemburnya itu seperti apa,"
  },
  {
    "user_id": 299017,
    "timestamp": "24:40-24:46",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "terus juga kalau untuk melewati kalau misalkan lebih dari batas maksimum si lembur tersebut apa yang bisa mereka"
  },
  {
    "user_id": 299017,
    "timestamp": "24:46-24:52",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "dapetin gitu untuk mengejar si eh work life balance-nya mereka, gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "24:55-25:46",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Ada lagi mungkin dari teman-teman yang lain yang belum ke cover dari Mbak Ulum dan aku."
  },
  {
    "user_id": 299019,
    "timestamp": "25:47-26:19",
    "user_name": "Imelda",
    "transcript": "Oke, aku izin menambahkan  ya. Tapi sebelumnya eh thank you banget nih untuk Mbak Ranti dan Mbak Ulum. Aku setuju ya, itu salah satu eh root cause yang menurutku bisa menyebabkan burnout gitu ya untuk para si karyawan gitu. Aku sangat setuju dan kalau aku memposisikan diri sebagai karyawan tersebut juga eh mungkin kalau misalnya disuruh ada opsi gitu ya kita bisa terapkan juga yang namanya penerapan manajemen proyek gitu, yang mana"
  },
  {
    "user_id": 299019,
    "timestamp": "26:17-26:23",
    "user_name": "Imelda",
    "transcript": "ketika memang ada proyek, kita tuh harus bisa em, ibaratnya dianalisis dulu nih secara timeline-nya itu eh, sejauh mana gitu ya."
  },
  {
    "user_id": 299019,
    "timestamp": "26:24-27:02",
    "user_name": "Imelda",
    "transcript": "Secara timeline, terus juga secara kayak mitigasi gitu ya. Apakah dari nanti dari timeline ini tuh opsi A B C. Apakah dari nanti dari timeline ini tuh opsi A B C-nya, event-nya itu ada kayak hal-hal yang perlu kita mitigasi enggak sih kayak gitu. Dan juga kita juga perlu eh kalau misal ada proyek masuk kan tentu kita sehabisnya itu ada evaluasi ya. Nah, evaluasi ini juga bisa kita jadikan acuan untuk ada di proyek ke depannya gitu. Kemudian juga eh selain itu Kak gitu. Kemudian juga eh selain itu kan itu juga bisa diimplementasikan ya. Dan kalaupun ada case baru gitu ya yang bisa menyebabkan kembali lagi nih ke bebannya tadi, itu juga tentu bisa kita sesuaikan juga gitu ya, sesuai dengan kesepakatan bersama."
  },
  {
    "user_id": 299019,
    "timestamp": "27:03-27:48",
    "user_name": "Imelda",
    "transcript": "Dan lagi-lagi juga kayaknya kita perlu juga untuk membuka sesi kayak eh apa ya masing-masing pendapat dari karyawannya. misalnya nih, eh membuka form gitu ya, sejenis form gitu. Jadi kita bisa tahu nih pikiran karyawan tuh seperti apa sih gitu. Nah dari situ kita juga bisa analisis nih, root cause-nya dari karyawan apa, terus kita bisa analisis juga root cause-nya dari segi kalian apa, gitu. Dan ini juga eh berkesinambungan dengan pendapat nya Mbak Ulum dan juga Mbak Ranti ya gitu ya. Eh dengan kurangnya karyawan tentu kita makanya perlu juga pendapat dari mereka gitu juga dan kurangnya SOP terkait lembur, gitu. Itu dariku sih guys. Mungkin kalau teman-teman ada tambahan, boleh banget."
  },
  {
    "user_id": 299022,
    "timestamp": "27:49-27:50",
    "user_name": "Siti Salma Aulia",
    "transcript": "Thank you, Mbak Imel. Mungkin dari Mbak Putri dulu boleh?"
  },
  {
    "user_id": 299022,
    "timestamp": "29:08-31:11",
    "user_name": "Siti Salma Aulia",
    "transcript": "Baik, thank you Mbak Putri. Thank you banget insight-nya teman-teman. Jadi aku menambahkan sepertinya ada yang belum di-state oleh teman-teman. Seingatku di case study itu adanya ada salah satu isu yaitu kesenjangan antara visi perusahaan sama real kondisinya nih. Jadi misi perusahaan kita itu sebenarnya untuk menjaga kesejahteraan karyawan. Tapi sayangnya untuk saat ini belum terealisasi dengan optimal dengan juga support dari isu-isu yang tadi teman-teman sebutkan.Jadi, eh, thank you berat. Tadi berarti kita sudah bisa sum up beberapa masalah ya, yang bisa kita bawa nantinya untuk penyelesaian solusinya ya. Jadi tadi pertama terkait SOP yang belum matang atau belum jelas. Lalu juga kurangnya karyawan, tadi kalau tidak salah eh karyawan 40 untuk project yang cukup banyak ternyata belum tentu cukup untuk menangani banyak event besar secara bersamaan. Lalu juga banyaknya karyawan yang burnout yang bisa juga berdampak dengan penurunan kualitas layanan kita ke klien. Lalu juga pertumbuhannya semakin pesat, tapi sayangnya belum adanya tools yang bisa mengoptimalkan kerja kita jadi lebih cepat dan juga jadi kurang personal seperti itu. Nah, dari teman-teman untuk sum up yang tadi aku buat, apakah ada yang mau ditambahkan? Kalau misalnya ada yang mau ditambahkan, boleh ditambahkan. Kalau sudah tidak ada yang perlu ditambah, kita bisa langsung masuk ke tahapan penyelesaian."
  },
  {
    "user_id": 299017,
    "timestamp": "31:12-31:39",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Dari aku aman sih. Aman kok. Paling di mungkin di-breakdown satu-satu ya terkait dengan permasalahannya tadi yang apa yang perlu dilakukan dan mungkin ngelihat dulu kali ya. jadi setiap case kan pasti ada positif negatifnya ya. Gitu. Jadi biar kita juga bisa ngambil konklusinya yang sebaik-baiknya untuk kalian karyawan dan sebaik-baiknya buat perusahaan juga gitu karena pada dasarnya kan kita harus bareng-bareng nih gitu bukan cuman mikirin perusahaan doang atau mikirin karyawannya aja. Gitu."
  },
  {
    "user_id": 299022,
    "timestamp": "31:12-32:10",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, thank you Ranti. Sip. Eh dari statementnya Ranti tersebut berarti kita juga perlu memikirkan dari dua pihak ya, kita sebagai karyawan lalu juga sebagai perusahaan yang sekarang kita kerjai gitu ya. sekarang kalau sudah bisa lanjut ke solusinya, dari teman-teman mungkin ada yang mau raise dulu kah solusi dari beberapa isu yang tadi kita state?"
  },
  {
    "user_id": 299017,
    "timestamp": "32:13-34:21",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Aku boleh enggak soal yang ini sih, soal manpower ya. Tadi kan kita juga sempat ngebahas. Jadi case-nya memang manpower-nya kurang tapi proyek selalu masuk gitu ya. Nah, menurutku tadi aku setuju banget sama Mbak Putri kayak kita harus ngelihat dulu job desk setiap orangnya seperti apa. Jadi itu bahasanya lebih ke kita harus menilai beban. Jadi itu bahasanya lebih ke kita harus menilai beban kerja setiap posisinya itu seperti apa gitu. Dan ketika kalau misalkan memang sudah tidak bisa ke-cover sama satu orang, itu tuh eh faktornya bisa banyak sih dilihat dari tugasnya apa, dari berapa lama dia mengerjakan tugas tersebut gitu ya. Kalau misalkan itu memang kurang, eh berarti tandanya Kalau misalkan itu memang kurang, eh berarti tandanya eh kita harus nambah orang gitu. Nah, tapi kita enggak bisa langsung menyimpulkan kayak oh ya udah deh, ternyata itu kurang nih orangnya harusnya dua orang, tapi ternyata pas kita cek lebih jauh, ternyata eh mungkin kualitas atau performance-nya orang tersebut itu yang kurang justru. Jadi mereka yang harusnya bisa optimal tapi malah jadi enggak optimal. Jadi ya kayak apa ya, kesannya kita ee menghambur-hamburkan potensi dan uang juga ya, yang kita investasikan karena kan karyawan investasi juga ya. Gitu. Nah, ee berarti salah satu caranya kita juga bisa lihat ke kualitasnya juga gitu. Jadi untuk mencari talen yang bekerja di perusahaan, jangan asal-asalan gitu. jangan cuman kayak oh oke, dia tapi ternyata eh dia itu belum bisa memegang nih posisi yang dia sekarang gitu. Jadi belum pernah megang yang jadinya dia harus belajar lagi dari nol kan. Itu akan prosesnya lebih lama lagi dan untuk kalau aku lihat kan event organizer ini kan pace-nya cepat banget ya. Gitu. Jadi emang butuh orang yang benar-benar bisa langsung mengejar pace-nya perusahaan tersebut gitu. Jadi lebih dari awal pun dari rekrutmennya emang benar-benar harus kuat banget sih dan di dalamnya untuk talent manajemennya perlu benar-benar dilihat gitu. Jadi kita juga dari perusahaan bisa menentukan mana ini yang emang butuh orang atau mungkin ya kasarnya orangnya harus diganti karena tidak fit in dengan posisinya kayak gitu."
  },
  {
    "user_id": 299020,
    "timestamp": "34:22-35:53",
    "user_name": "Putri Rosalinda",
    "transcript": "aku juga setuju sih. Kalau aku setuju dengan yang Mbak Ranti mention ya, tapi sebelum itu juga kita mungkin perlu tes dulu ya, kayak misalnya yang kita lakuin sekarang tapi sebelum itu juga kita mungkin perlu tes dulu ya, kayak misalnya yang kita lakuin sekarang juga bisa kita lakuin di case ini gitu ya, dengan kita buat study case ke mereka, and then kita bisa elaborasi dari yang udah ada nih apa apa yang perlu kita eh apa namanya perbaiki ya, terutama tadi ada di mention juga kalau administrasinya gitu ya berantakan, terus kemudian ada yang dia vendornya mintanya buru-buru berarti kan enggak ada SOP-nya. terus kemudian ada yang dia vendornya mintanya buru-buru berarti kan enggak ada SLA-nya, terus berarti berantakan lah ibaratnya ya. Jadi mungkin awal yang paling mula adalah eh apa namanya menilai karyawannya dulu gitu ya. kedua adalah setelah menilai kita bisa tahu nih eh apa namanya perlu diganti atau perlu nambah orang atau seperti apa gitu atau memang mereka tuh alasan doang kita kan enggak tahu ya yang sebenarnya tuh terjadi. And then yang ketiga berarti setelah setelah itu setelah sudah dapat eh apa namanya? hasil penilaiannya terus kita dapat sih yang kedua kita juga bisa bikin SOP dulu ya, bikin SOP, kemudian eh gimana sih eh yang terbaik nih di apa namanya? di eh EO, EO ya namanya ya? Event organizer gitu kan. Kita bisa benchmark ke yang udah besar. And then gimana sih yang terbaik nih di apa namanya? di EO, EO ya namanya ya, event organizer gitu kan. Kita bisa benchmark ke yang udah besar. And then yang ketiga, kita bisa lakuin apa namanya? eh yang diskusi lebih dalam ya terkait dengan keuangan. Oh, uangnya ini bisanya yang mana aja gitu. Apa kita bisa pakai automation kayak mungkin Darwin Box gitu ya. Ini kompleks banget gitu. Padahal cuma EO tapi kita kan kayaknya butuh gitu ya, gitu. Maksudnya seperti itu. Mungkin dari aku itu sih. Kalau dari kalau tanggapan dari aku."
  },
  {
    "user_id": 299022,
    "timestamp": "35:58-37:40",
    "user_name": "Siti Salma Aulia",
    "transcript": "Thank you, Mbak Putri. Mungkin aku boleh menambahkan ya. Aku agree dengan statement dari Mbak Putri, Mbak Ranti. Mungkin aku juga mau menambahkan seperti tadi dari Mbak Ranti dan Mbak Putri berkaitan dengan evaluasi dan juga adanya tools tambahan untuk mempermudah pekerjaan karyawan. Nah, dari aku mau menambahkan seperti eh implementasi sistem reward dan juga apresiasi nih ke karyawan yang mereka sudah benar-benar overtime walaupun tidak sesuai dengan yang mereka bayangkan, kita bisa dengan kita bisa dengan menambahkan implementasi performance bonus, lalu juga sistem employee of the month, atau kita juga bahkan bisa mengapresiasi grup berbasis kayak per project EO gitu ya. Jadi dengan itu ekspektasinya karyawan itu bisa merasakan jadi lebih terlihat dan terapresiate oleh perusahaan ini. Nah, aku juga setuju nih sama statementnya Mbak Putri terkait SLA yang belum jelas dari kita dan klien. Jadi menurut aku mengelola ekspektasi klien itu penting. Kita juga perlu transparan terhadap klien mengenai timeline realistisnya kapan, lalu tim kapasitas kita ada berapa banyak. Jadi menurut aku untuk SLA ini kita bisa perketat SLA dan juga mengurangi praktik last minute request dari klien. Dari aku seperti itu. Mungkin ada tambahan dari Mbak Ulum dan Mbak Imel?"
  },
  {
    "user_id": 299021,
    "timestamp": "37:45-39:00",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Eh, aku ada tambahan dikit ya. Tadi aku setuju sama Mbak Ranti gitu. Kita eh sebelum kita cari orang gitu ya ibaratnya, eh kita harus tahu dulu, lihat dulu apakah emang kita butuh eh staf yang full time di kita atau justru beberapa pekerjaan kita bisa arahin ke freelancer atau mungkin orang-orang yang kerjanya part time gitu untuk kita. Nah, kalau seandainya kita udah memang bisa analisis itu, mana kebutuhan yang memang bisa kita eh bagiin untuk full timer dan juga freelancer gitu ya. Nanti kita baru bisa bagi eh selanjutnya kita mau ambil ke mana nih, mau emang ambil full timer atau freelancer karena kan nge apa ya? nge-rekrut orang itu kan juga butuh ini ya, eh sumber daya-sumber daya lainnya gitu kayak biaya, terus juga waktu, dan cari orang yang kompeten itu juga enggak gampang gitu. Jadi, em mungkin beberapa pekerjaan yang simpel dan sifatnya itu bisa dialihkan dengan cepat  ke freelancer itu kita bisa pakai gitu. Kita bisa pakai strategi itu gitu."
  },
  {
    "user_id": 299017,
    "timestamp": "39:01-39:03",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Betul, betul. Dari Mbak Imel mungkin ada tambahan enggak?"
  },
  {
    "user_id": 299019,
    "timestamp": "39:06-40:50",
    "user_name": "Imelda",
    "transcript": "Iya, boleh. eh aku juga izin nambahin ya. Sebetulnya aku juga sangat setuju ya dengan semua sarannya karena itu juga udah menyangkut efisiensi untuk kantor kita gitu ya. Dan sebelum eh tadi ya, sebelum kita mungkin kayak ngerekrut orang baru, sebetulnya eh aku jadi kepikiran juga nih untuk kita bisa tahu nih sebetulnya keterampilan karyawan kita tuh di mana aja sih gitu. Karena tadi aku lihat case-nya ada kesenjangan keterampilan gitu. Mungkin kalau misalnya kita bisa tes nih, misalnya si karyawan A tuh punya eh passion di ABC gitu. Tapi di role dia, passion-nya dia itu hanya AB aja nih yang dipakai. Nah, kalaupun dia ada passion C yang bisa untuk membantu divisi lain, nah, itu kan sebetulnya sangat memungkinkan ya untuk bisa memperbantukan divisi lain ketika lagi ada load yang cukup banyak gitu. Tapi kembali lagi kepada SOP eh jatuhnya lembur atau gimana gitu. Sehingga eh dari sisi karyawan dan dari sisi perusahaan tuh bisa paling oke gitu. Karena pasti ada karyawan yang memang mereka tuh punya eh kayak sikap yang mungkin kerja keras gitu ya. Jadi tanpa memandang kayak ah ya udahlah dikerjain aja nih gitu ya. Demi pokoknya. Demi pokoknya demi kebaikan bersama lah gitu. Pasti ada kok karyawan yang kayak gitu. Gitu. Jadi kita bisa untuk me-manage dengan itu salah satunya. Selain itu, eh tadi dengan kata Baulung juga, kita juga bisa ambil dari freelance atau part time kayak gitu sih. Jadi aku eh sih nambahin kalau konteksnya di awal kita untuk ngerekrut orang, itu yang kepikiran di aku gitu ya. Jadi kita bisa menganalisis eh passion-nya karyawan. IT tambahin kalau konteksnya di awal kita untuk nge-recruit orang, itu yang kepikiran di aku gitu ya. Jadi kita bisa nganalisis eh passion karyawan gitu ya, enggak cuman passion di role-nya dia aja. Kayak gitu. dan yang poin-poin lainnya aku juga sangat setuju ya tadi ya untuk analisis job desc-nya dia. Apakah dia memang eh perform-nya yang turun gitu ya, enggak optimal atau enggak gitu. Itu kan semua bisa dianalisis juga. Gitu sih kalau tambahan dari aku, guys. Thank you."
  },
  {
    "user_id": 299022,
    "timestamp": "40:51-41:03",
    "user_name": "Siti Salma Aulia",
    "transcript": "Thank you, Mbak Imel. Nah, sepertinya tadi kita semua sudah memberikan solusi. Mungkin dari teman-teman ada yang mau ditambahkan dulu solusinya sebelum kita jump ke prioritization?"
  },
  {
    "user_id": 299017,
    "timestamp": "41:04-41:07",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Iya. Eh, boleh? Eh Mbak Ulum dulu aja."
  },
  {
    "user_id": 299021,
    "timestamp": "41:08-43:15",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Oke. Iya. Em, tadi itu kan diriku juga sempat baca gitu ya, kalau adanya eh apa namanya? burnout karyawan itu menyebabkan turnover karyawan juga meningkat gitu kan. Nah, sema- itu mungkin tadi belum di-mention tapi aku baru ingat juga kalau itu terjadi. Nah, turnover karyawan yang meningkat ini kan juga menyebabkan akhirnya orang-orang yang ba- eh banyak orang baru gitu dan belum ngerti cara kerja di eh kantor kita gitu kan. Atau eh nah, mungkin kita juga bisa mulai ini ya, ada pembagian eh apa ya, mungkin pembagian job desk gitu ya. Tadi kan ada divisi-divisi yang kerjanya ngerjain project, ada yang enggak gitu kan. Nah, mungkin divisi-divisi yang eh enggak ngerjain project nih kayak finance, HC itu dia udah eh mereka mulai eh memperbaiki gitu standar di perusahaan kita gitu. Sementara teman-teman yang fokus di project, eh mereka bisa memperbaiki gimana nih manajemen projectnya. Jadi eh apa namanya? kita bagi tugas lah ya istilahnya untuk eh meningkatkan gimana caranya meningkatkan kesejahteraan karyawan. Selain itu juga eh kita minta untuk karyawan-karyawan yang memang resign gitu ya, yang meninggalkan pekerjaannya di sini untuk mendokumentasikan proses kerjanya. Nah, itu kan dokumentasi-dokumentasi itu bisa kita pakai untuk eh karyawan-karyawan baru atau yang akan ngegantiin mereka eh mempercepat proses mereka belajar gitu. sehingga itu mempermudah kita untuk eh apa ya, ngelatih karyawan baru ini tadi untuk bisa fit in di project yang mereka lakuin gitu. Kayak gitu sih mungkin ya.Selain itu juga tadi nambahin dari Mbak Salma, em kayaknya bisa juga gitu kita pertimbangkan mungkin sistem bonus akhir tahun gitu kan, karena kan ini kan banyak project gede gitu yang kita ambil. Gitu sih mungkin dari aku. Nah mungkin boleh dilanjut Mbak Ranti."
  },
  {
    "user_id": 299017,
    "timestamp": "43:16-45:18",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Iya sama sih tadi juga aku kepikiran buat case yang jadi ada statement di mana katanya pola karena pola kerjanya dinamis, jadi untuk beberapa divisi itu berat sebelah gitu yang di mana memungkinkan banget kalau berat sebelah itu berarti burn out-nya cuman di beberapa divisi doang, enggak merata gitu. Nah, menurut aku itu tuh bisa terjadi karena mungkin eh kan kita sebagai perusahaan pasti akan selalu ingin eh bigger bigger and bigger dengan cara ya mungkin kita bekerja sama-sama banyak klien, segala macam, tapi mungkin di situ ada gap di mana adanya enggak kurang komunikasi antar tim gitu loh. Jadi mungkin tim yang garda terdepan yang menerima klien kayak ya udah kita ambil-ambilin dulu semua gitu. Tapi kita tidak melihat kondisi di lapangan atau kondisi yang mengerjakan project tersebut itu, apakah masih full banget atau misalkan bisa enggak megang nih si klien-klien yang masuk ini gitu. Karena menurutku kan karyawan tuh merupakan investasi ya gitu buat perusahaan dan itu tuh benar-benar aset yang harus dijaga banget karena ya kalau enggak ada karyawan ya enggak ada yang bisa ngerjain gitu. Nah, jadi eh gap di situnya sih yang perlu diperhatikan sama si perusahaan tersebut. Jangan hanya memikirkan klien tapi eh apa? karyawannya pun harus di eh pikirkan gitu. Dan aku setuju banget sama Mbak Ulum terkait dengan eh pembelajaran untuk orang yang baru gitu karena mayoritas mungkin orang-orang akan fokus lebih ke ke depan gitu ya. Jadi lebih ke klien base, terus kita juga gimana cara kita growth segala macam tapi kita enggak mikirin untuk orang yang akan baru masuk gitu. yang jadinya akan ada gap di situ. Jadi kalau ketika ada orang baru atau misalkan ada divisi baru itu mereka akan tertinggal banget karena tidak punya dokumentasi atau tidak punya pembelajaran yang standarnya tuh tinggi gitu. Jadi nanti bisa aja kita ngulang lagi nyari lagi orang, ngulang lagi nyari timnya karena enggak fit in itu. Jadi aku setuju banget sih."
  },
  {
    "user_id": 299020,
    "timestamp": "45:20-45:36",
    "user_name": "Putri Rosalinda",
    "transcript": "Sama sih, tapi sebelum eh maaf, tapi sebelum ke solusi-solusi itu kita juga mulai harus ada survei internal dulu kali ya. Karena kan kita enggak tahu nih tingginya turnover karyawan itu sebenarnya karena apa gitu kan.Dari survei itu baru bisa masuk ke dalam solusi-solusi yang tadi sudah disebutkan Mbak Ranti dan yang lain. Mungkin tambahan dari aku itu sih. Thank you."
  },
  {
    "user_id": 299022,
    "timestamp": "45:38-45:55",
    "user_name": "Siti Salma Aulia",
    "transcript": "Thank you Mbak Putri, thank you. Yang lainnya juga ada tambahan lagi kah sebelum kita move ke tahapan selanjutnya?"
  },
  {
    "user_id": 299017,
    "timestamp": "45:46-46:16",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Enggak sih. Paling tadi yang belum kebahas soal yang otomasi yang perlu diprovide itu seperti apa? Karena kan tadi juga sempat ada case kayak event-nya tuh udah mulai ke berbasis teknologi dan ramah lingkungan. Tapi mereka semua masih manual gitu. Jadi ya kan eh apa ya? bentuk otomasi atau prioritas seperti apa gitu ya, yang perlu dibuat, yang didahulukan kali ya, biar semuanya berjalan dengan baik, gitu."
  },
  {
    "user_id": 299022,
    "timestamp": "46:17-46:32",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oh iya ya, aku agree itu belum sempat terbahas oleh kita. Mungkin ada pendapat atau statement lain kah untuk jadi tambahan pembahasan kita di solusi ini untuk automation tools-nya? Dari teman-teman?"
  },
  {
    "user_id": 299020,
    "timestamp": "46:33-46:35",
    "user_name": "Putri Rosalinda",
    "transcript": "Mungkin otomasi itu ya, eh maaf Mbak Ulum. Maaf."
  },
  {
    "user_id": 299021,
    "timestamp": "46:36-46:38",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Enggak apa-apa, lanjut aja Mbak Putri."
  },
  {
    "user_id": 299020,
    "timestamp": "46:43-47:36",
    "user_name": "Putri Rosalinda",
    "transcript": "Mungkin otomasi di bagian apa? kayak modul HRIS dulu kali ya, kalau menurut aku. Karena kan ini yang parah adalah SOP ya sama SLA. Terus di mana eh otomasinya itu kita mencakupnya fokusnya enggak enggak enggak kompleks dulu tapi ya memang mungkin kan budget juga ya. Mungkin ke dalam misalnya SOP, kemudian job desk-nya, kemudian benefit yang pantasnya seperti apa, terus nanti dikelola sama HR. Terus kemudian eh baru setelah itu, setelah itu udah oke misalnya ya, baru mungkin masuk ke automasi yang lain gitu ya. Itu sih kalau dari aku. ya, mungkin bisa pakai HRIS eh atau mungkin bisa pakai yang kalau mau sekalian ada sih, aku pernah baca tuh yang bisa semua sekalian kayak rekrutmen semua all in ke perusahaan juga bisa gitu ya.  Tapi itu kan tergantung dengan budget sih. Mungkin kalau budget kita banyak, kita bisa langsung all in. Jadi eh pelan-pelan untuk merubah gitu kalau murah gitu budgetnya. Tapi kalau misalnya eh apa budgetnya enggak ada ya kita bisa fokus ke yang tadi dulu gitu sih kalau dari aku. Mungkin dari Mbak Ulum, silakan."
  },
  {
    "user_id": 299021,
    "timestamp": "47:37-49:02",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Iya, eh kalau dari diriku sih lebih ke otomasi di tim project-nya ya, gitu. Karena kan yang load-nya besar itu di mereka gitu kan. Dan eh mungkin beberapa hal kayak eh tim art designer itu mereka mungkin ada tools-tools yang memudahkan mereka untuk eh ngeproses desain gitu. Jadi eh misal udah ada sketsa yang firm gitu untuk eh apa gitu ya, mungkin banner atau eh sketsa untuk layout eh tempat acara gitu. Jadi mereka sudah punya apa ya? semacam referensi-referensi yang bisa mereka langsung pakai. Jadi enggak scratch dari awal gitu. Nah, hal-hal kayak gitu menurut aku penting ya, karena kan tadi dibilang juga kalau desain itu seringkali ee tiba-tiba berubah gitu di vendornya gitu. dan eh kalau perubahan produksi di vendor itu kan kita juga harus bayar dobel gitu kan. Itu pengaruh juga ke finansial kita gitu. Jadi eh menurut aku di otomasi proses kerja di tim project itu kayaknya eh perlu gitu dan itu cukup urgen untuk dilakukan karena kalau enggak dan terus-terusan manual, makin lama kan project masuk terus nih gitu kan. Nah, itu makin makin banyak nih hal yang harus di dilakuin gitu kalau manual sistemnya, gitu sih."
  },
  {
    "user_id": 299022,
    "timestamp": "49:03-49:05",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, thank you Mbak Ulum. Ada tambahan lagikah?"
  },
  {
    "user_id": 299019,
    "timestamp": "49:05-49:06",
    "user_name": "Imelda",
    "transcript": "Aku boleh deh."
  },
  {
    "user_id": 299022,
    "timestamp": "49:06-49:07",
    "user_name": "Siti Salma Aulia",
    "transcript": "Boleh Mel."
  },
  {
    "user_id": 299019,
    "timestamp": "49:08-49:55",
    "user_name": "Imelda",
    "transcript": "Oke, eh jadi aku izin nambahin guys. Aku sangat setuju juga ya dengan pada eh pendapat teman-teman gitu ya. Tapi kalau konteksnya ke persiapan untuk kita bisa memutuskan automation itu, eh baiknya kita juga bisa tahu dulu nih, automation itu eh bisa digunakan untuk tas apa aja, untuk divisi mana aja, maksudnya untuk divisi mana aja tuh mungkin dari automation itu bisa"
  },
  {
    "user_id": 299022,
    "timestamp": "49:56-50:05",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, sip, thank you Mbak Imel. Mungkin ada tambahan lagi sebelum kita jump ke priorisi- eh prioritization solusi yang tadi kita paparkan sebelumnya?"
  },
  {
    "user_id": 299020,
    "timestamp": "50:05-50:06",
    "user_name": "Putri Rosalinda",
    "transcript": "Aman. Makasih."
  },
  {
    "user_id": 299022,
    "timestamp": "50:07-51:49",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, aman semua. Oke, sip. Jadi menurutku setelah kita brainstorm terhadap solusi-solusi dari teman-teman, menurutku untuk implementasinya kita perlu mengambil langkah-langkah nih. Pertama kayak kita perlu pilih dari solusi sebanyak itu mungkin either tiga atau lima solusi utama yang prioritasnya tinggi dan juga bisa segera dieksekusi. Jadi tuh menurutku perlu banget kita tahu nih apa aja sih yang mau kita kejar, lalu ekspektasinya di kapan, sama siapa yang responsible untuk melakukan solusi tersebut. Nah, kalau misal dari teman-teman belum clear sama statement dariku, mungkin aku berikan contoh ya. Dari case kita tadi sebelumnya, aku bisa memberikan statement bahwa evaluasi beban kerja ataupun melakukan audit ya terhadap beban kerja yang tidak sebanding antar tim itu ekspektasi dari aku bisa dilakukan cepat. Mungkin ekspektasinya minggu ini, terus siapa yang responsibel? Nah, ini bisa dibantu sama teman-teman HC ataupun HR. Jadi ekspektasinya, prioritas ini dilakukan adalah kita agar lebih aware pertama nih terkait tidak fair-nya beban kerja yang dilakukan di beberapa divisi. Nah, itu dariku, dari teman-teman monggo pendapatnya."
  },
  {
    "user_id": 299017,
    "timestamp": "51:49-54:25",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Kalau dari aku sebenarnya aku juga setuju ya kita kita mungkin ngebagi fokusnya di tiga ya mungkin untuk eh sori. Buat pekerjanya itu sendiri, terus untuk ke klien sama untuk mungkin lebih ke automasi dan di luar itulah ya gitu. Nah, memang karyawan ini yang perlu di duluanin sih diprioritaskan karena enggak ada klien kalau karyawannya juga enggak ada gitu. Jadi prioritas menurut aku yang pertama lebih ke kayak kita benar-benar ngejar di luar yang tadi Salma bilangin ya, gitu. Kita benar-benar ngejar terkait dengan SOP yang ajeg di perusahaan tersebut untuk karyawan dari soal lembur, soal beban kerja dan lain-lain karena kalau misalkan dari situ sudah clear, jadi ketika itu udah di luar workload-nya atau segala macam, kita bisa langsung gampang mitigasinya. Gitu. Kalau misalkan enggak ada SOP yang jelas, orang tuh kan cenderung akan lebih loss ya gitu kayak, aduh aku bingung nih, aduh aku ini harusnya gimana ya? Segala macam. Nah, pihak dari perusahaannya pun juga lebih enak gitu untuk menjelaskannya kalau udah ada SOP yang jelas. Kayak kalau misalkan memang eh apa? Oh ternyata dia sudah banyak lembur gitu. Itu kan eh udah melewati batasnya juga.Berarti kan kita juga sebagai HR-nya mungkin ya bisa membela atau misalkan bilang kayak memang dia udah kebanyakan dan dia jatuhnya udah burnout yang jadinya enggak baik. Gitu. Jadi dari SOP-nya juga memang harus jelas seiring dengan kita sambil asesmen teman-teman di dalamnya itu seperti apa kondisinya. Enggak cuman dari kerjaan doang tapi dari mentalnya juga gitu. karena itu benar-benar eh salah satu kuncian biar at least mereka work life balance di luar dari bonus ya gitu. karena kan kalau bonus segala macam itu itu dikembalikan lagi ke perusahaan ya gitu. Gimana kondisinya mereka apakah eh bisa atau enggak gitu. Cuman kalau at least kalau kita bisa ngejaga mentalnya teman-teman tuh itu udah jadi daya apa ya? Bukan daya jual, maksudnya yang bisa dipertahankan sih, biar teman-temannya at least terjaga lah gitu. Work life balance-nya gitu. dan ya manajemen project itu sangat-sangat penting sih gitu. Jadi jangan asal udah diterima, terima terus nih kerjaannya. Tapi mereka harus beneran nyusun timeline sedetail mungkin gitu ya. Mungkin untuk beberapa orang itu akan sulit gitu ya. Bukan bukan untuk orang yang bukan detail oriented. Cuman itu akan membantu semua orang untuk bisa ngelihat apakah bisa dihandle enggak pekerjaan di luar yang ini tuh, gitu."
  },
  {
    "user_id": 299020,
    "timestamp": "54:26-55:53",
    "user_name": "Putri Rosalinda",
    "transcript": "Oke, kalau dari aku. Kalau menurut aku boleh ditambahin ya. Kalau dari aku eh dari dari dari kasus yang ada ini kan fokusnya ke bagian vendor ya yang memang ngejelimet. Jadi kalau dari aku mungkin yang pertama kali kita perlu eh apa eh selesaikan gitu ya. Itu adalah tim yang tadi sempat Mbak Ulum singgung ya yaitu tim project ya atau tim yang mengeksekusi bagian vendor. Mungkin itu yang pertama kali bisa kita eh solusikan dulu ya di bagian divisi itu. Nanti mungkin tim HR bisa lakukan dulu dari mulai ngebuat SLE, eh SOP-nya bersama dengan mungkin eh apa namanya? CEO atau eh apa? di C level ya. Jadi fokus ke tim yang memang ke vendor dulu nih yang memang ke yang jual gitu. Karena kan kita kan jual jasa ya gitu. Itu fokus ke situ dulu. Baru setelah itu kalau misalnya kita ada duitnya baru ke bagian divisi lain untuk eh masuk ke kesejahteraan mereka. Karena kan fokusnya ini ke turnover ya, ke kesejahteraan karyawan. Baru setelah eh itu kan kita fokusnya kalau mereka udah happy nih, eh SOP udah ada gitu ya. Kan masuk ke yang kedua tadi sempat dimention sama siapa namanya? Sama Mbak Salma ya, Mbak Salma bahwa automation karena automation yang tadi sempat Mbak Ulum mention juga perlu untuk automation di proses tim project-nya dulu yang memang berhubungan sama vendor ya atau klien. Karena kan yang paling utama ujung tombaknya mereka gitu ya. Kalau mereka dulu, mereka enggak eh enggak di enggak diusuin, enggak diduluin itu kan pasti eh bakal apa apa namanya revenue juga bakal turun. Itu sih kalau dari aku mungkin tambahannya."
  },
  {
    "user_id": 299021,
    "timestamp": "55:55-56:38",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Ya kalau dari aku mungkin eh ini ya, eh agree sama Mbak Ranti sama Mbak Putri. Cuman kalau menurut aku untuk perbaikan SOP dan perbaikan manajemen project itu kayaknya bisa jalan bersamaan ya, gitu. Heeh. Gitu sih. Jadi karena kan yang ngelakuin dua dua tim yang berbeda gitu kan. Jadi kayaknya bisa jalan bareng-bareng dan eh nanti implementasinya mungkin juga bisa bareng gitu. karena ehm karena eksekutornya kan beda dan eh untuk si level yang penanggung jawabnya pun juga aku lihatnya berbeda gitu di di organisasi kita gitu. Jadi kayaknya bisa untuk eh jalan barengan gitu sih Mbak Salma, Mbak Imel dan Mbak Ranti dan Mbak Putri gitu ya."
  },
  {
    "user_id": 299022,
    "timestamp": "56:44-56:50",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, thank you Mbak Ulum. Mungkin ada tambahan dari Mbak Imel?"
  },
  {
    "user_id": 299019,
    "timestamp": "49:59-50:06",
    "user_name": "Imelda",
    "transcript": "Tentunya kan kita juga udah tahu nih, evaluasinya apa, kemudian bisa kita eh kurasi lagi ya, poin-poin dari segi klien, segi karyawan, dan juga perusahaan. Dari situ juga kita bisa analisis pro dan kontranya gitu. Misalnya ada opsi A. Nah, pro-nya apa, kontranya apa? Dan menurutku dari situ kita juga bisa analisis lagi nih keputusan yang terbaik itu seperti apa. Termasuk juga ke automation tadi, gitu. Jadi sebetulnya kalau memang melakukan sesuatu untuk perbaikan, aku rasa memang semua dari evaluasi dari manajemen proyek dan juga dari pro dan kontra dari apa yang sudah kita putuskan. Sehingga itu akan merembet ke hal-hal lain eh maksudnya ke tindakan yang mau kita implement. Gitu sih Kak. Thank you."
  },
  {
    "user_id": 299022,
    "timestamp": "57:45-57:50",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke Imel. Oke guys, ada tambahan lagikah?"
  },
  {
    "user_id": 299017,
    "timestamp": "57:51-58:31",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Udah sih berarti kalau yang aku tangkap ya, jadi sebenarnya eh semua tuh jalannya barengan. Jadi kita juga fokus ke ke karyawan iya, ke klien iya, ke perusahaan iya gitu. Jadi jangan ada yang duluan-duluanin kali ya gitu. Semuanya jadi priority utama. Cuman kita harus bisa menentukan aja siapa yang betul kata Salma, siapa yang jadi racinya ya bahasanya gitu. Siapa yang responsible, siapa yang mengerjakan segala macam. Jadi biar output-nya itu bisa berjalan berjalan bersamaan dan semoga si burn out dan ya yang dirasakan sama karyawan itu enggak terjadi lagi sih kalau semua sudah dievaluasi dari tiga sudut pandang itu."
  },
  {
    "user_id": 299020,
    "timestamp": "58:32-59:02",
    "user_name": "Putri Rosalinda",
    "transcript": "Sama budget juga ya. Jadi menurutku yang paling penting dulu tuh bagian ke klien dulu ya nanti mungkin kita buat skala priority, mungkin yang P P zero, P one, segala macam yang biasa kita lakuin juga gitu ya guys ya. Jadi mungkin kalau udah ada itunya, kita jadi bisa eh masuk nih ke budget-nya yang mana. Karena kan apalagi modelnya eh apa? automation itu kan kalau kita lihat juga pasti banyak budget yang keluar ya. Eh seperti itu sih kalau dari sisi AQ. Tambahannya. Makasih."
  },
  {
    "user_id": 299022,
    "timestamp": "59:07-59:12",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, sip, thank you Mbak Putri. Mbak Imel, Mbak Ranti, mungkin dari Mbak Ulum ada sedikit kah?"
  },
  {
    "user_id": 299021,
    "timestamp": "59:15-59:27",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Eh dari aku cukup sih, Mbak. Masih sama dengan teman-teman yang lain ya. Tadi juga apa yang jadi aku sampaikan juga eh sudah apa? menambahkan dari apa yang disampaikan teman-teman lain."
  },
  {
    "user_id": 299022,
    "timestamp": "59:28-1:00:42",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, siap. Thank you teman-teman. Solusinya benar-benar insightful banget nih. Jadi kalau kita sudah oke untuk kita sum up, berarti kita sudah bisa masuk ke kesimpulan kah untuk apa aja sih yang akan kita lakukan ataupun menyelesaikan solusi ini? Kalau sudah, gini, guys. Oke good. Siap, oke. Nah, kalau sudah semua, nanti boleh dibantu kita sum up bareng-bareng ya, teman-teman. Jadi, aku coba sum up dulu dari beberapa statement yang teman-teman berikan ke aku ya. Jadi, pertama, penting adanya evaluasi ataupun audit beban kerja beserta pro cons-nya dari segala sisi. Baik itu dari karyawan, dari perusahaan maupun dari klien. Lalu yang kedua, pentingnya ada automation tools untuk membantu kerja kita agar jadi lebih optimal, bisa lebih cepat ataupun bahkan projectnya nanti akan lebih banyak juga itu berkesinambungan. Lalu yang ketiga juga tadi ada hubungannya dengan budget. Nah, dari teman-teman ada tambahan kah yang mau di-sum up dari segmen yang tadi aku berikan."
  },
  {
    "user_id": 299021,
    "timestamp": "1:00:44-1:00:58",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Eh, ya, terkait apa namanya? untuk perbaikan SOP dan juga manajemen project itu berarti sudah masuk ke bagian audit beban kerja dan juga evaluasi ya, Mbak Salma ya?"
  },
  {
    "user_id": 299022,
    "timestamp": "1:01:01-1:01:08",
    "user_name": "Siti Salma Aulia",
    "transcript": "Mmm, aku belum memasukkan, tapi mungkin untuk lebih clear-nya betul. Kita perlu adanya perbaikan SOP manajemen, betul. Thank you, Mbak Ulum. Dari yang lain ada tambahan lagikah?"
  },
  {
    "user_id": 299020,
    "timestamp": "1:01:11-1:01:21",
    "user_name": "Putri Rosalinda",
    "transcript": "Dari aku sama ya. Eh mungkin sama itu yang paling penting SOP dulu. Karena kan kalau SOP dan SLA-nya aja juga udah jelas, kita bisa kelanjut ke tahap yang lain ya, seperti itu."
  },
  {
    "user_id": 299022,
    "timestamp": "1:01:11-1:01:21",
    "user_name": "Siti Salma Aulia",
    "transcript": "Mantap. Dari Mbak Imel? Sudah oke?"
  },
  {
    "user_id": 299019,
    "timestamp": "1:01:24-1:01:21",
    "user_name": "Imelda",
    "transcript": "Oke, kalau dariku sudah oke. Mungkin aku izin menambahkan kayak perlu monitoring berkala aja sih untuk bisa tahu sejauh mana eh apa itu berjalan efektif, efektif efisien atau enggak. Gitu, guys."
  },
  {
    "user_id": 299022,
    "timestamp": "1:01:22-1:01:43",
    "user_name": "Siti Salma Aulia",
    "transcript": "Mantap mantap. Oke, sip. Dari Mbak Imel udah, dari Mbak Ulum udah, dari Mbak Putri udah, dari Ranti sudah oke semua?"
  },
  {
    "user_id": 299017,
    "timestamp": "1:01:44-1:01:45",
    "user_name": "Admiranti Adhyaricka Wardhani Kusuma",
    "transcript": "Udah oke, udah ke-cover semuanya sama teman-teman."
  },
  {
    "user_id": 299022,
    "timestamp": "1:01:46-1:01:53",
    "user_name": "Siti Salma Aulia",
    "transcript": "Oke, kalau sudah ke-cover semuanya berarti apakah sudah kita bisa sudahi LGD ini mba Nada?"
  },
  {
    "user_id": 299018,
    "timestamp": "1:01:56-1:02:16",
    "user_name": "Assessment Rakamin",
    "transcript": "Oke, kalau memang sudah tidak ada yang mau ditambahkan dari hasil eh pemaparan kesimpulan tadi, eh untuk sesi LGD di siang hari ini bisa kita cukupkan sampai sini. Terima kasih teman-teman. Mungkin nanti eh Adiriku akan eh kalau mau ada pertanyaan terkait dengan asesmennya bisa ditanyakan ke tim HC-nya ya, seperti itu. Eh terima kasih sudah meluangkan waktunya. Selamat siang semua."
  },
  {
    "user_id": 299019,
    "timestamp": "1:02:17-1:02:18",
    "user_name": "Imelda",
    "transcript": "Terima kasih."
  },
  {
    "user_id": 299020,
    "timestamp": "1:02:19-1:02:20",
    "user_name": "Putri Rosalinda",
    "transcript": "Dadah."
  },
  {
    "user_id": 299021,
    "timestamp": "1:02:21-1:02:22",
    "user_name": "Inayah Ulum Mufidah",
    "transcript": "Thank you. Salam kenal."
  },
  {
    "user_id": 299022,
    "timestamp": "1:02:23-1:02:24",
    "user_name": "Siti Salma Aulia",
    "transcript": "Dadah."
  }
]
      