import React from 'react';

const Sidebar = ({ activeEvalType, onEvalTypeChange }) => {
  const sidebarStyle = {
    width: '250px',
    backgroundColor: '#f8f9fa',
    borderRight: '1px solid #dee2e6',
    padding: '20px',
    height: '100vh',
    position: 'sticky',
    top: 0
  };

  const menuItemStyle = {
    padding: '12px 16px',
    margin: '8px 0',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontWeight: '500',
    border: 'none',
    width: '100%',
    textAlign: 'left',
    fontSize: '14px'
  };

  const activeMenuItemStyle = {
    ...menuItemStyle,
    backgroundColor: '#007bff',
    color: 'white'
  };

  const inactiveMenuItemStyle = {
    ...menuItemStyle,
    backgroundColor: 'white',
    color: '#495057',
    border: '1px solid #dee2e6'
  };

  return (
    <div style={sidebarStyle}>
      <h3 style={{ 
        marginBottom: '24px', 
        color: '#495057',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        Evaluation Types
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <button
          style={activeEvalType === 'idp' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('idp')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'idp') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'idp') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          📋 IDP Recommendation
        </button>
        
        <button
          style={activeEvalType === 'lgd' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('lgd')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'lgd') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'lgd') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          👥 LGD Analysis
        </button>
      </div>

      <div style={{ 
        marginTop: '32px', 
        padding: '16px', 
        backgroundColor: '#e3f2fd', 
        borderRadius: '6px',
        fontSize: '12px',
        color: '#1565c0'
      }}>
        <strong>Current:</strong><br />
        {activeEvalType === 'idp' ? 'IDP Recommendation Evaluations' : 'LGD Analysis Evaluations'}
      </div>
    </div>
  );
};

export default Sidebar;
