(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=n(i);fetch(i.href,l)}})();var dl=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Eu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var dp={exports:{}},Wl={},hp={exports:{}},Q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xi=Symbol.for("react.element"),mg=Symbol.for("react.portal"),gg=Symbol.for("react.fragment"),yg=Symbol.for("react.strict_mode"),xg=Symbol.for("react.profiler"),kg=Symbol.for("react.provider"),wg=Symbol.for("react.context"),vg=Symbol.for("react.forward_ref"),Sg=Symbol.for("react.suspense"),Cg=Symbol.for("react.memo"),Eg=Symbol.for("react.lazy"),Qa=Symbol.iterator;function bg(e){return e===null||typeof e!="object"?null:(e=Qa&&e[Qa]||e["@@iterator"],typeof e=="function"?e:null)}var mp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},gp=Object.assign,yp={};function gr(e,t,n){this.props=e,this.context=t,this.refs=yp,this.updater=n||mp}gr.prototype.isReactComponent={};gr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};gr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function xp(){}xp.prototype=gr.prototype;function bu(e,t,n){this.props=e,this.context=t,this.refs=yp,this.updater=n||mp}var Tu=bu.prototype=new xp;Tu.constructor=bu;gp(Tu,gr.prototype);Tu.isPureReactComponent=!0;var Ga=Array.isArray,kp=Object.prototype.hasOwnProperty,Ru={current:null},wp={key:!0,ref:!0,__self:!0,__source:!0};function vp(e,t,n){var r,i={},l=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(l=""+t.key),t)kp.call(t,r)&&!wp.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var u=Array(s),a=0;a<s;a++)u[a]=arguments[a+2];i.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:xi,type:e,key:l,ref:o,props:i,_owner:Ru.current}}function Tg(e,t){return{$$typeof:xi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Pu(e){return typeof e=="object"&&e!==null&&e.$$typeof===xi}function Rg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ka=/\/+/g;function vo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Rg(""+e.key):t.toString(36)}function Xi(e,t,n,r,i){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case xi:case mg:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+vo(o,0):r,Ga(i)?(n="",e!=null&&(n=e.replace(Ka,"$&/")+"/"),Xi(i,t,n,"",function(a){return a})):i!=null&&(Pu(i)&&(i=Tg(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Ka,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Ga(e))for(var s=0;s<e.length;s++){l=e[s];var u=r+vo(l,s);o+=Xi(l,t,n,u,i)}else if(u=bg(e),typeof u=="function")for(e=u.call(e),s=0;!(l=e.next()).done;)l=l.value,u=r+vo(l,s++),o+=Xi(l,t,n,u,i);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function _i(e,t,n){if(e==null)return e;var r=[],i=0;return Xi(e,r,"","",function(l){return t.call(n,l,i++)}),r}function Pg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Fe={current:null},Ji={transition:null},_g={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:Ji,ReactCurrentOwner:Ru};function Sp(){throw Error("act(...) is not supported in production builds of React.")}Q.Children={map:_i,forEach:function(e,t,n){_i(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return _i(e,function(){t++}),t},toArray:function(e){return _i(e,function(t){return t})||[]},only:function(e){if(!Pu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Q.Component=gr;Q.Fragment=gg;Q.Profiler=xg;Q.PureComponent=bu;Q.StrictMode=yg;Q.Suspense=Sg;Q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_g;Q.act=Sp;Q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=gp({},e.props),i=e.key,l=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,o=Ru.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)kp.call(t,u)&&!wp.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&s!==void 0?s[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){s=Array(u);for(var a=0;a<u;a++)s[a]=arguments[a+2];r.children=s}return{$$typeof:xi,type:e.type,key:i,ref:l,props:r,_owner:o}};Q.createContext=function(e){return e={$$typeof:wg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:kg,_context:e},e.Consumer=e};Q.createElement=vp;Q.createFactory=function(e){var t=vp.bind(null,e);return t.type=e,t};Q.createRef=function(){return{current:null}};Q.forwardRef=function(e){return{$$typeof:vg,render:e}};Q.isValidElement=Pu;Q.lazy=function(e){return{$$typeof:Eg,_payload:{_status:-1,_result:e},_init:Pg}};Q.memo=function(e,t){return{$$typeof:Cg,type:e,compare:t===void 0?null:t}};Q.startTransition=function(e){var t=Ji.transition;Ji.transition={};try{e()}finally{Ji.transition=t}};Q.unstable_act=Sp;Q.useCallback=function(e,t){return Fe.current.useCallback(e,t)};Q.useContext=function(e){return Fe.current.useContext(e)};Q.useDebugValue=function(){};Q.useDeferredValue=function(e){return Fe.current.useDeferredValue(e)};Q.useEffect=function(e,t){return Fe.current.useEffect(e,t)};Q.useId=function(){return Fe.current.useId()};Q.useImperativeHandle=function(e,t,n){return Fe.current.useImperativeHandle(e,t,n)};Q.useInsertionEffect=function(e,t){return Fe.current.useInsertionEffect(e,t)};Q.useLayoutEffect=function(e,t){return Fe.current.useLayoutEffect(e,t)};Q.useMemo=function(e,t){return Fe.current.useMemo(e,t)};Q.useReducer=function(e,t,n){return Fe.current.useReducer(e,t,n)};Q.useRef=function(e){return Fe.current.useRef(e)};Q.useState=function(e){return Fe.current.useState(e)};Q.useSyncExternalStore=function(e,t,n){return Fe.current.useSyncExternalStore(e,t,n)};Q.useTransition=function(){return Fe.current.useTransition()};Q.version="18.3.1";hp.exports=Q;var K=hp.exports;const _u=Eu(K);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ag=K,Lg=Symbol.for("react.element"),Ig=Symbol.for("react.fragment"),zg=Object.prototype.hasOwnProperty,Og=Ag.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,jg={key:!0,ref:!0,__self:!0,__source:!0};function Cp(e,t,n){var r,i={},l=null,o=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)zg.call(t,r)&&!jg.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Lg,type:e,key:l,ref:o,props:i,_owner:Og.current}}Wl.Fragment=Ig;Wl.jsx=Cp;Wl.jsxs=Cp;dp.exports=Wl;var w=dp.exports,fs={},Ep={exports:{}},nt={},bp={exports:{}},Tp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,B){var k=O.length;O.push(B);e:for(;0<k;){var J=k-1>>>1,te=O[J];if(0<i(te,B))O[J]=B,O[k]=te,k=J;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var B=O[0],k=O.pop();if(k!==B){O[0]=k;e:for(var J=0,te=O.length,S=te>>>1;J<S;){var we=2*(J+1)-1,ft=O[we],oe=we+1,St=O[oe];if(0>i(ft,k))oe<te&&0>i(St,ft)?(O[J]=St,O[oe]=k,J=oe):(O[J]=ft,O[we]=k,J=we);else if(oe<te&&0>i(St,k))O[J]=St,O[oe]=k,J=oe;else break e}}return B}function i(O,B){var k=O.sortIndex-B.sortIndex;return k!==0?k:O.id-B.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var o=Date,s=o.now();e.unstable_now=function(){return o.now()-s}}var u=[],a=[],c=1,f=null,p=3,d=!1,h=!1,x=!1,E=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(O){for(var B=n(a);B!==null;){if(B.callback===null)r(a);else if(B.startTime<=O)r(a),B.sortIndex=B.expirationTime,t(u,B);else break;B=n(a)}}function v(O){if(x=!1,y(O),!h)if(n(u)!==null)h=!0,ae(T);else{var B=n(a);B!==null&&ge(v,B.startTime-O)}}function T(O,B){h=!1,x&&(x=!1,m(A),A=-1),d=!0;var k=p;try{for(y(B),f=n(u);f!==null&&(!(f.expirationTime>B)||O&&!j());){var J=f.callback;if(typeof J=="function"){f.callback=null,p=f.priorityLevel;var te=J(f.expirationTime<=B);B=e.unstable_now(),typeof te=="function"?f.callback=te:f===n(u)&&r(u),y(B)}else r(u);f=n(u)}if(f!==null)var S=!0;else{var we=n(a);we!==null&&ge(v,we.startTime-B),S=!1}return S}finally{f=null,p=k,d=!1}}var C=!1,_=null,A=-1,N=5,b=-1;function j(){return!(e.unstable_now()-b<N)}function F(){if(_!==null){var O=e.unstable_now();b=O;var B=!0;try{B=_(!0,O)}finally{B?W():(C=!1,_=null)}}else C=!1}var W;if(typeof g=="function")W=function(){g(F)};else if(typeof MessageChannel<"u"){var ee=new MessageChannel,V=ee.port2;ee.port1.onmessage=F,W=function(){V.postMessage(null)}}else W=function(){E(F,0)};function ae(O){_=O,C||(C=!0,W())}function ge(O,B){A=E(function(){O(e.unstable_now())},B)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){h||d||(h=!0,ae(T))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(O){switch(p){case 1:case 2:case 3:var B=3;break;default:B=p}var k=p;p=B;try{return O()}finally{p=k}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,B){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var k=p;p=O;try{return B()}finally{p=k}},e.unstable_scheduleCallback=function(O,B,k){var J=e.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?J+k:J):k=J,O){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=k+te,O={id:c++,callback:B,priorityLevel:O,startTime:k,expirationTime:te,sortIndex:-1},k>J?(O.sortIndex=k,t(a,O),n(u)===null&&O===n(a)&&(x?(m(A),A=-1):x=!0,ge(v,k-J))):(O.sortIndex=te,t(u,O),h||d||(h=!0,ae(T))),O},e.unstable_shouldYield=j,e.unstable_wrapCallback=function(O){var B=p;return function(){var k=p;p=B;try{return O.apply(this,arguments)}finally{p=k}}}})(Tp);bp.exports=Tp;var Ng=bp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dg=K,tt=Ng;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Rp=new Set,Yr={};function On(e,t){ur(e,t),ur(e+"Capture",t)}function ur(e,t){for(Yr[e]=t,e=0;e<t.length;e++)Rp.add(t[e])}var Mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ps=Object.prototype.hasOwnProperty,Fg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Xa={},Ja={};function Mg(e){return ps.call(Ja,e)?!0:ps.call(Xa,e)?!1:Fg.test(e)?Ja[e]=!0:(Xa[e]=!0,!1)}function Bg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ug(e,t,n,r){if(t===null||typeof t>"u"||Bg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Me(e,t,n,r,i,l,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new Me(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new Me(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new Me(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new Me(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new Me(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new Me(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new Me(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new Me(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new Me(e,5,!1,e.toLowerCase(),null,!1,!1)});var Au=/[\-:]([a-z])/g;function Lu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Au,Lu);Re[t]=new Me(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Au,Lu);Re[t]=new Me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Au,Lu);Re[t]=new Me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new Me(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new Me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new Me(e,1,!1,e.toLowerCase(),null,!0,!0)});function Iu(e,t,n,r){var i=Re.hasOwnProperty(t)?Re[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ug(t,n,i,r)&&(n=null),r||i===null?Mg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var $t=Dg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ai=Symbol.for("react.element"),$n=Symbol.for("react.portal"),Vn=Symbol.for("react.fragment"),zu=Symbol.for("react.strict_mode"),ds=Symbol.for("react.profiler"),Pp=Symbol.for("react.provider"),_p=Symbol.for("react.context"),Ou=Symbol.for("react.forward_ref"),hs=Symbol.for("react.suspense"),ms=Symbol.for("react.suspense_list"),ju=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),Ap=Symbol.for("react.offscreen"),Ya=Symbol.iterator;function br(e){return e===null||typeof e!="object"?null:(e=Ya&&e[Ya]||e["@@iterator"],typeof e=="function"?e:null)}var de=Object.assign,So;function Nr(e){if(So===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);So=t&&t[1]||""}return`
`+So+e}var Co=!1;function Eo(e,t){if(!e||Co)return"";Co=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(a){r=a}e.call(t.prototype)}else{try{throw Error()}catch(a){r=a}e()}}catch(a){if(a&&r&&typeof a.stack=="string"){for(var i=a.stack.split(`
`),l=r.stack.split(`
`),o=i.length-1,s=l.length-1;1<=o&&0<=s&&i[o]!==l[s];)s--;for(;1<=o&&0<=s;o--,s--)if(i[o]!==l[s]){if(o!==1||s!==1)do if(o--,s--,0>s||i[o]!==l[s]){var u=`
`+i[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=s);break}}}finally{Co=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Nr(e):""}function Hg(e){switch(e.tag){case 5:return Nr(e.type);case 16:return Nr("Lazy");case 13:return Nr("Suspense");case 19:return Nr("SuspenseList");case 0:case 2:case 15:return e=Eo(e.type,!1),e;case 11:return e=Eo(e.type.render,!1),e;case 1:return e=Eo(e.type,!0),e;default:return""}}function gs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vn:return"Fragment";case $n:return"Portal";case ds:return"Profiler";case zu:return"StrictMode";case hs:return"Suspense";case ms:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case _p:return(e.displayName||"Context")+".Consumer";case Pp:return(e._context.displayName||"Context")+".Provider";case Ou:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ju:return t=e.displayName||null,t!==null?t:gs(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return gs(e(t))}catch{}}return null}function $g(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gs(t);case 8:return t===zu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function an(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Lp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Vg(e){var t=Lp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,l.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Li(e){e._valueTracker||(e._valueTracker=Vg(e))}function Ip(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Lp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function hl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ys(e,t){var n=t.checked;return de({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Za(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=an(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function zp(e,t){t=t.checked,t!=null&&Iu(e,"checked",t,!1)}function xs(e,t){zp(e,t);var n=an(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ks(e,t.type,n):t.hasOwnProperty("defaultValue")&&ks(e,t.type,an(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ec(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ks(e,t,n){(t!=="number"||hl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Dr=Array.isArray;function tr(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+an(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function ws(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return de({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function tc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(Dr(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:an(n)}}function Op(e,t){var n=an(t.value),r=an(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function nc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function jp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function vs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?jp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ii,Np=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ii=Ii||document.createElement("div"),Ii.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ii.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Zr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Br={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Wg=["Webkit","ms","Moz","O"];Object.keys(Br).forEach(function(e){Wg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Br[t]=Br[e]})});function Dp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Br.hasOwnProperty(e)&&Br[e]?(""+t).trim():t+"px"}function Fp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Dp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var qg=de({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ss(e,t){if(t){if(qg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Cs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Es=null;function Nu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bs=null,nr=null,rr=null;function rc(e){if(e=vi(e)){if(typeof bs!="function")throw Error(L(280));var t=e.stateNode;t&&(t=Xl(t),bs(e.stateNode,e.type,t))}}function Mp(e){nr?rr?rr.push(e):rr=[e]:nr=e}function Bp(){if(nr){var e=nr,t=rr;if(rr=nr=null,rc(e),t)for(e=0;e<t.length;e++)rc(t[e])}}function Up(e,t){return e(t)}function Hp(){}var bo=!1;function $p(e,t,n){if(bo)return e(t,n);bo=!0;try{return Up(e,t,n)}finally{bo=!1,(nr!==null||rr!==null)&&(Hp(),Bp())}}function ei(e,t){var n=e.stateNode;if(n===null)return null;var r=Xl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Ts=!1;if(Mt)try{var Tr={};Object.defineProperty(Tr,"passive",{get:function(){Ts=!0}}),window.addEventListener("test",Tr,Tr),window.removeEventListener("test",Tr,Tr)}catch{Ts=!1}function Qg(e,t,n,r,i,l,o,s,u){var a=Array.prototype.slice.call(arguments,3);try{t.apply(n,a)}catch(c){this.onError(c)}}var Ur=!1,ml=null,gl=!1,Rs=null,Gg={onError:function(e){Ur=!0,ml=e}};function Kg(e,t,n,r,i,l,o,s,u){Ur=!1,ml=null,Qg.apply(Gg,arguments)}function Xg(e,t,n,r,i,l,o,s,u){if(Kg.apply(this,arguments),Ur){if(Ur){var a=ml;Ur=!1,ml=null}else throw Error(L(198));gl||(gl=!0,Rs=a)}}function jn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Vp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ic(e){if(jn(e)!==e)throw Error(L(188))}function Jg(e){var t=e.alternate;if(!t){if(t=jn(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var l=i.alternate;if(l===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===l.child){for(l=i.child;l;){if(l===n)return ic(i),e;if(l===r)return ic(i),t;l=l.sibling}throw Error(L(188))}if(n.return!==r.return)n=i,r=l;else{for(var o=!1,s=i.child;s;){if(s===n){o=!0,n=i,r=l;break}if(s===r){o=!0,r=i,n=l;break}s=s.sibling}if(!o){for(s=l.child;s;){if(s===n){o=!0,n=l,r=i;break}if(s===r){o=!0,r=l,n=i;break}s=s.sibling}if(!o)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function Wp(e){return e=Jg(e),e!==null?qp(e):null}function qp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=qp(e);if(t!==null)return t;e=e.sibling}return null}var Qp=tt.unstable_scheduleCallback,lc=tt.unstable_cancelCallback,Yg=tt.unstable_shouldYield,Zg=tt.unstable_requestPaint,ye=tt.unstable_now,ey=tt.unstable_getCurrentPriorityLevel,Du=tt.unstable_ImmediatePriority,Gp=tt.unstable_UserBlockingPriority,yl=tt.unstable_NormalPriority,ty=tt.unstable_LowPriority,Kp=tt.unstable_IdlePriority,ql=null,_t=null;function ny(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(ql,e,void 0,(e.current.flags&128)===128)}catch{}}var yt=Math.clz32?Math.clz32:ly,ry=Math.log,iy=Math.LN2;function ly(e){return e>>>=0,e===0?32:31-(ry(e)/iy|0)|0}var zi=64,Oi=4194304;function Fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function xl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,l=e.pingedLanes,o=n&268435455;if(o!==0){var s=o&~i;s!==0?r=Fr(s):(l&=o,l!==0&&(r=Fr(l)))}else o=n&~i,o!==0?r=Fr(o):l!==0&&(r=Fr(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,l=t&-t,i>=l||i===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-yt(t),i=1<<n,r|=e[n],t&=~i;return r}function oy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function sy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-yt(l),s=1<<o,u=i[o];u===-1?(!(s&n)||s&r)&&(i[o]=oy(s,t)):u<=t&&(e.expiredLanes|=s),l&=~s}}function Ps(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Xp(){var e=zi;return zi<<=1,!(zi&4194240)&&(zi=64),e}function To(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ki(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-yt(t),e[t]=n}function uy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-yt(n),l=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~l}}function Fu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-yt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var Z=0;function Jp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Yp,Mu,Zp,ed,td,_s=!1,ji=[],en=null,tn=null,nn=null,ti=new Map,ni=new Map,Xt=[],ay="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function oc(e,t){switch(e){case"focusin":case"focusout":en=null;break;case"dragenter":case"dragleave":tn=null;break;case"mouseover":case"mouseout":nn=null;break;case"pointerover":case"pointerout":ti.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ni.delete(t.pointerId)}}function Rr(e,t,n,r,i,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[i]},t!==null&&(t=vi(t),t!==null&&Mu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function cy(e,t,n,r,i){switch(t){case"focusin":return en=Rr(en,e,t,n,r,i),!0;case"dragenter":return tn=Rr(tn,e,t,n,r,i),!0;case"mouseover":return nn=Rr(nn,e,t,n,r,i),!0;case"pointerover":var l=i.pointerId;return ti.set(l,Rr(ti.get(l)||null,e,t,n,r,i)),!0;case"gotpointercapture":return l=i.pointerId,ni.set(l,Rr(ni.get(l)||null,e,t,n,r,i)),!0}return!1}function nd(e){var t=wn(e.target);if(t!==null){var n=jn(t);if(n!==null){if(t=n.tag,t===13){if(t=Vp(n),t!==null){e.blockedOn=t,td(e.priority,function(){Zp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=As(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Es=r,n.target.dispatchEvent(r),Es=null}else return t=vi(n),t!==null&&Mu(t),e.blockedOn=n,!1;t.shift()}return!0}function sc(e,t,n){Yi(e)&&n.delete(t)}function fy(){_s=!1,en!==null&&Yi(en)&&(en=null),tn!==null&&Yi(tn)&&(tn=null),nn!==null&&Yi(nn)&&(nn=null),ti.forEach(sc),ni.forEach(sc)}function Pr(e,t){e.blockedOn===t&&(e.blockedOn=null,_s||(_s=!0,tt.unstable_scheduleCallback(tt.unstable_NormalPriority,fy)))}function ri(e){function t(i){return Pr(i,e)}if(0<ji.length){Pr(ji[0],e);for(var n=1;n<ji.length;n++){var r=ji[n];r.blockedOn===e&&(r.blockedOn=null)}}for(en!==null&&Pr(en,e),tn!==null&&Pr(tn,e),nn!==null&&Pr(nn,e),ti.forEach(t),ni.forEach(t),n=0;n<Xt.length;n++)r=Xt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Xt.length&&(n=Xt[0],n.blockedOn===null);)nd(n),n.blockedOn===null&&Xt.shift()}var ir=$t.ReactCurrentBatchConfig,kl=!0;function py(e,t,n,r){var i=Z,l=ir.transition;ir.transition=null;try{Z=1,Bu(e,t,n,r)}finally{Z=i,ir.transition=l}}function dy(e,t,n,r){var i=Z,l=ir.transition;ir.transition=null;try{Z=4,Bu(e,t,n,r)}finally{Z=i,ir.transition=l}}function Bu(e,t,n,r){if(kl){var i=As(e,t,n,r);if(i===null)No(e,t,r,wl,n),oc(e,r);else if(cy(i,e,t,n,r))r.stopPropagation();else if(oc(e,r),t&4&&-1<ay.indexOf(e)){for(;i!==null;){var l=vi(i);if(l!==null&&Yp(l),l=As(e,t,n,r),l===null&&No(e,t,r,wl,n),l===i)break;i=l}i!==null&&r.stopPropagation()}else No(e,t,r,null,n)}}var wl=null;function As(e,t,n,r){if(wl=null,e=Nu(r),e=wn(e),e!==null)if(t=jn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Vp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return wl=e,null}function rd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ey()){case Du:return 1;case Gp:return 4;case yl:case ty:return 16;case Kp:return 536870912;default:return 16}default:return 16}}var Yt=null,Uu=null,Zi=null;function id(){if(Zi)return Zi;var e,t=Uu,n=t.length,r,i="value"in Yt?Yt.value:Yt.textContent,l=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[l-r];r++);return Zi=i.slice(e,1<r?1-r:void 0)}function el(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ni(){return!0}function uc(){return!1}function rt(e){function t(n,r,i,l,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=l,this.target=o,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(l):l[s]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Ni:uc,this.isPropagationStopped=uc,this}return de(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ni)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ni)},persist:function(){},isPersistent:Ni}),t}var yr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Hu=rt(yr),wi=de({},yr,{view:0,detail:0}),hy=rt(wi),Ro,Po,_r,Ql=de({},wi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$u,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==_r&&(_r&&e.type==="mousemove"?(Ro=e.screenX-_r.screenX,Po=e.screenY-_r.screenY):Po=Ro=0,_r=e),Ro)},movementY:function(e){return"movementY"in e?e.movementY:Po}}),ac=rt(Ql),my=de({},Ql,{dataTransfer:0}),gy=rt(my),yy=de({},wi,{relatedTarget:0}),_o=rt(yy),xy=de({},yr,{animationName:0,elapsedTime:0,pseudoElement:0}),ky=rt(xy),wy=de({},yr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vy=rt(wy),Sy=de({},yr,{data:0}),cc=rt(Sy),Cy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ey={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},by={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ty(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=by[e])?!!t[e]:!1}function $u(){return Ty}var Ry=de({},wi,{key:function(e){if(e.key){var t=Cy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=el(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ey[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$u,charCode:function(e){return e.type==="keypress"?el(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?el(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Py=rt(Ry),_y=de({},Ql,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fc=rt(_y),Ay=de({},wi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$u}),Ly=rt(Ay),Iy=de({},yr,{propertyName:0,elapsedTime:0,pseudoElement:0}),zy=rt(Iy),Oy=de({},Ql,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jy=rt(Oy),Ny=[9,13,27,32],Vu=Mt&&"CompositionEvent"in window,Hr=null;Mt&&"documentMode"in document&&(Hr=document.documentMode);var Dy=Mt&&"TextEvent"in window&&!Hr,ld=Mt&&(!Vu||Hr&&8<Hr&&11>=Hr),pc=" ",dc=!1;function od(e,t){switch(e){case"keyup":return Ny.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function sd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function Fy(e,t){switch(e){case"compositionend":return sd(t);case"keypress":return t.which!==32?null:(dc=!0,pc);case"textInput":return e=t.data,e===pc&&dc?null:e;default:return null}}function My(e,t){if(Wn)return e==="compositionend"||!Vu&&od(e,t)?(e=id(),Zi=Uu=Yt=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ld&&t.locale!=="ko"?null:t.data;default:return null}}var By={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!By[e.type]:t==="textarea"}function ud(e,t,n,r){Mp(r),t=vl(t,"onChange"),0<t.length&&(n=new Hu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $r=null,ii=null;function Uy(e){kd(e,0)}function Gl(e){var t=Gn(e);if(Ip(t))return e}function Hy(e,t){if(e==="change")return t}var ad=!1;if(Mt){var Ao;if(Mt){var Lo="oninput"in document;if(!Lo){var mc=document.createElement("div");mc.setAttribute("oninput","return;"),Lo=typeof mc.oninput=="function"}Ao=Lo}else Ao=!1;ad=Ao&&(!document.documentMode||9<document.documentMode)}function gc(){$r&&($r.detachEvent("onpropertychange",cd),ii=$r=null)}function cd(e){if(e.propertyName==="value"&&Gl(ii)){var t=[];ud(t,ii,e,Nu(e)),$p(Uy,t)}}function $y(e,t,n){e==="focusin"?(gc(),$r=t,ii=n,$r.attachEvent("onpropertychange",cd)):e==="focusout"&&gc()}function Vy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Gl(ii)}function Wy(e,t){if(e==="click")return Gl(t)}function qy(e,t){if(e==="input"||e==="change")return Gl(t)}function Qy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:Qy;function li(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ps.call(t,i)||!wt(e[i],t[i]))return!1}return!0}function yc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xc(e,t){var n=yc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=yc(n)}}function fd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?fd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function pd(){for(var e=window,t=hl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=hl(e.document)}return t}function Wu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gy(e){var t=pd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fd(n.ownerDocument.documentElement,n)){if(r!==null&&Wu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,l=Math.min(r.start,i);r=r.end===void 0?l:Math.min(r.end,i),!e.extend&&l>r&&(i=r,r=l,l=i),i=xc(n,l);var o=xc(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ky=Mt&&"documentMode"in document&&11>=document.documentMode,qn=null,Ls=null,Vr=null,Is=!1;function kc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Is||qn==null||qn!==hl(r)||(r=qn,"selectionStart"in r&&Wu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Vr&&li(Vr,r)||(Vr=r,r=vl(Ls,"onSelect"),0<r.length&&(t=new Hu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=qn)))}function Di(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Qn={animationend:Di("Animation","AnimationEnd"),animationiteration:Di("Animation","AnimationIteration"),animationstart:Di("Animation","AnimationStart"),transitionend:Di("Transition","TransitionEnd")},Io={},dd={};Mt&&(dd=document.createElement("div").style,"AnimationEvent"in window||(delete Qn.animationend.animation,delete Qn.animationiteration.animation,delete Qn.animationstart.animation),"TransitionEvent"in window||delete Qn.transitionend.transition);function Kl(e){if(Io[e])return Io[e];if(!Qn[e])return e;var t=Qn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in dd)return Io[e]=t[n];return e}var hd=Kl("animationend"),md=Kl("animationiteration"),gd=Kl("animationstart"),yd=Kl("transitionend"),xd=new Map,wc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fn(e,t){xd.set(e,t),On(t,[e])}for(var zo=0;zo<wc.length;zo++){var Oo=wc[zo],Xy=Oo.toLowerCase(),Jy=Oo[0].toUpperCase()+Oo.slice(1);fn(Xy,"on"+Jy)}fn(hd,"onAnimationEnd");fn(md,"onAnimationIteration");fn(gd,"onAnimationStart");fn("dblclick","onDoubleClick");fn("focusin","onFocus");fn("focusout","onBlur");fn(yd,"onTransitionEnd");ur("onMouseEnter",["mouseout","mouseover"]);ur("onMouseLeave",["mouseout","mouseover"]);ur("onPointerEnter",["pointerout","pointerover"]);ur("onPointerLeave",["pointerout","pointerover"]);On("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));On("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));On("onBeforeInput",["compositionend","keypress","textInput","paste"]);On("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));On("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));On("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yy=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function vc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Xg(r,t,void 0,e),e.currentTarget=null}function kd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],u=s.instance,a=s.currentTarget;if(s=s.listener,u!==l&&i.isPropagationStopped())break e;vc(i,s,a),l=u}else for(o=0;o<r.length;o++){if(s=r[o],u=s.instance,a=s.currentTarget,s=s.listener,u!==l&&i.isPropagationStopped())break e;vc(i,s,a),l=u}}}if(gl)throw e=Rs,gl=!1,Rs=null,e}function se(e,t){var n=t[Ds];n===void 0&&(n=t[Ds]=new Set);var r=e+"__bubble";n.has(r)||(wd(t,e,2,!1),n.add(r))}function jo(e,t,n){var r=0;t&&(r|=4),wd(n,e,r,t)}var Fi="_reactListening"+Math.random().toString(36).slice(2);function oi(e){if(!e[Fi]){e[Fi]=!0,Rp.forEach(function(n){n!=="selectionchange"&&(Yy.has(n)||jo(n,!1,e),jo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Fi]||(t[Fi]=!0,jo("selectionchange",!1,t))}}function wd(e,t,n,r){switch(rd(t)){case 1:var i=py;break;case 4:i=dy;break;default:i=Bu}n=i.bind(null,t,n,e),i=void 0,!Ts||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function No(e,t,n,r,i){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;o=o.return}for(;s!==null;){if(o=wn(s),o===null)return;if(u=o.tag,u===5||u===6){r=l=o;continue e}s=s.parentNode}}r=r.return}$p(function(){var a=l,c=Nu(n),f=[];e:{var p=xd.get(e);if(p!==void 0){var d=Hu,h=e;switch(e){case"keypress":if(el(n)===0)break e;case"keydown":case"keyup":d=Py;break;case"focusin":h="focus",d=_o;break;case"focusout":h="blur",d=_o;break;case"beforeblur":case"afterblur":d=_o;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":d=ac;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":d=gy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":d=Ly;break;case hd:case md:case gd:d=ky;break;case yd:d=zy;break;case"scroll":d=hy;break;case"wheel":d=jy;break;case"copy":case"cut":case"paste":d=vy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":d=fc}var x=(t&4)!==0,E=!x&&e==="scroll",m=x?p!==null?p+"Capture":null:p;x=[];for(var g=a,y;g!==null;){y=g;var v=y.stateNode;if(y.tag===5&&v!==null&&(y=v,m!==null&&(v=ei(g,m),v!=null&&x.push(si(g,v,y)))),E)break;g=g.return}0<x.length&&(p=new d(p,h,null,n,c),f.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",d=e==="mouseout"||e==="pointerout",p&&n!==Es&&(h=n.relatedTarget||n.fromElement)&&(wn(h)||h[Bt]))break e;if((d||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,d?(h=n.relatedTarget||n.toElement,d=a,h=h?wn(h):null,h!==null&&(E=jn(h),h!==E||h.tag!==5&&h.tag!==6)&&(h=null)):(d=null,h=a),d!==h)){if(x=ac,v="onMouseLeave",m="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(x=fc,v="onPointerLeave",m="onPointerEnter",g="pointer"),E=d==null?p:Gn(d),y=h==null?p:Gn(h),p=new x(v,g+"leave",d,n,c),p.target=E,p.relatedTarget=y,v=null,wn(c)===a&&(x=new x(m,g+"enter",h,n,c),x.target=y,x.relatedTarget=E,v=x),E=v,d&&h)t:{for(x=d,m=h,g=0,y=x;y;y=Bn(y))g++;for(y=0,v=m;v;v=Bn(v))y++;for(;0<g-y;)x=Bn(x),g--;for(;0<y-g;)m=Bn(m),y--;for(;g--;){if(x===m||m!==null&&x===m.alternate)break t;x=Bn(x),m=Bn(m)}x=null}else x=null;d!==null&&Sc(f,p,d,x,!1),h!==null&&E!==null&&Sc(f,E,h,x,!0)}}e:{if(p=a?Gn(a):window,d=p.nodeName&&p.nodeName.toLowerCase(),d==="select"||d==="input"&&p.type==="file")var T=Hy;else if(hc(p))if(ad)T=qy;else{T=Vy;var C=$y}else(d=p.nodeName)&&d.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(T=Wy);if(T&&(T=T(e,a))){ud(f,T,n,c);break e}C&&C(e,p,a),e==="focusout"&&(C=p._wrapperState)&&C.controlled&&p.type==="number"&&ks(p,"number",p.value)}switch(C=a?Gn(a):window,e){case"focusin":(hc(C)||C.contentEditable==="true")&&(qn=C,Ls=a,Vr=null);break;case"focusout":Vr=Ls=qn=null;break;case"mousedown":Is=!0;break;case"contextmenu":case"mouseup":case"dragend":Is=!1,kc(f,n,c);break;case"selectionchange":if(Ky)break;case"keydown":case"keyup":kc(f,n,c)}var _;if(Vu)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else Wn?od(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(ld&&n.locale!=="ko"&&(Wn||A!=="onCompositionStart"?A==="onCompositionEnd"&&Wn&&(_=id()):(Yt=c,Uu="value"in Yt?Yt.value:Yt.textContent,Wn=!0)),C=vl(a,A),0<C.length&&(A=new cc(A,e,null,n,c),f.push({event:A,listeners:C}),_?A.data=_:(_=sd(n),_!==null&&(A.data=_)))),(_=Dy?Fy(e,n):My(e,n))&&(a=vl(a,"onBeforeInput"),0<a.length&&(c=new cc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:a}),c.data=_))}kd(f,t)})}function si(e,t,n){return{instance:e,listener:t,currentTarget:n}}function vl(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,l=i.stateNode;i.tag===5&&l!==null&&(i=l,l=ei(e,n),l!=null&&r.unshift(si(e,l,i)),l=ei(e,t),l!=null&&r.push(si(e,l,i))),e=e.return}return r}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Sc(e,t,n,r,i){for(var l=t._reactName,o=[];n!==null&&n!==r;){var s=n,u=s.alternate,a=s.stateNode;if(u!==null&&u===r)break;s.tag===5&&a!==null&&(s=a,i?(u=ei(n,l),u!=null&&o.unshift(si(n,u,s))):i||(u=ei(n,l),u!=null&&o.push(si(n,u,s)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Zy=/\r\n?/g,ex=/\u0000|\uFFFD/g;function Cc(e){return(typeof e=="string"?e:""+e).replace(Zy,`
`).replace(ex,"")}function Mi(e,t,n){if(t=Cc(t),Cc(e)!==t&&n)throw Error(L(425))}function Sl(){}var zs=null,Os=null;function js(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ns=typeof setTimeout=="function"?setTimeout:void 0,tx=typeof clearTimeout=="function"?clearTimeout:void 0,Ec=typeof Promise=="function"?Promise:void 0,nx=typeof queueMicrotask=="function"?queueMicrotask:typeof Ec<"u"?function(e){return Ec.resolve(null).then(e).catch(rx)}:Ns;function rx(e){setTimeout(function(){throw e})}function Do(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),ri(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);ri(t)}function rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function bc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var xr=Math.random().toString(36).slice(2),Rt="__reactFiber$"+xr,ui="__reactProps$"+xr,Bt="__reactContainer$"+xr,Ds="__reactEvents$"+xr,ix="__reactListeners$"+xr,lx="__reactHandles$"+xr;function wn(e){var t=e[Rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Bt]||n[Rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=bc(e);e!==null;){if(n=e[Rt])return n;e=bc(e)}return t}e=n,n=e.parentNode}return null}function vi(e){return e=e[Rt]||e[Bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function Xl(e){return e[ui]||null}var Fs=[],Kn=-1;function pn(e){return{current:e}}function ue(e){0>Kn||(e.current=Fs[Kn],Fs[Kn]=null,Kn--)}function le(e,t){Kn++,Fs[Kn]=e.current,e.current=t}var cn={},ze=pn(cn),$e=pn(!1),Rn=cn;function ar(e,t){var n=e.type.contextTypes;if(!n)return cn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},l;for(l in n)i[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ve(e){return e=e.childContextTypes,e!=null}function Cl(){ue($e),ue(ze)}function Tc(e,t,n){if(ze.current!==cn)throw Error(L(168));le(ze,t),le($e,n)}function vd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(L(108,$g(e)||"Unknown",i));return de({},n,r)}function El(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,Rn=ze.current,le(ze,e),le($e,$e.current),!0}function Rc(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=vd(e,t,Rn),r.__reactInternalMemoizedMergedChildContext=e,ue($e),ue(ze),le(ze,e)):ue($e),le($e,n)}var jt=null,Jl=!1,Fo=!1;function Sd(e){jt===null?jt=[e]:jt.push(e)}function ox(e){Jl=!0,Sd(e)}function dn(){if(!Fo&&jt!==null){Fo=!0;var e=0,t=Z;try{var n=jt;for(Z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}jt=null,Jl=!1}catch(i){throw jt!==null&&(jt=jt.slice(e+1)),Qp(Du,dn),i}finally{Z=t,Fo=!1}}return null}var Xn=[],Jn=0,bl=null,Tl=0,it=[],lt=0,Pn=null,Nt=1,Dt="";function yn(e,t){Xn[Jn++]=Tl,Xn[Jn++]=bl,bl=e,Tl=t}function Cd(e,t,n){it[lt++]=Nt,it[lt++]=Dt,it[lt++]=Pn,Pn=e;var r=Nt;e=Dt;var i=32-yt(r)-1;r&=~(1<<i),n+=1;var l=32-yt(t)+i;if(30<l){var o=i-i%5;l=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Nt=1<<32-yt(t)+i|n<<i|r,Dt=l+e}else Nt=1<<l|n<<i|r,Dt=e}function qu(e){e.return!==null&&(yn(e,1),Cd(e,1,0))}function Qu(e){for(;e===bl;)bl=Xn[--Jn],Xn[Jn]=null,Tl=Xn[--Jn],Xn[Jn]=null;for(;e===Pn;)Pn=it[--lt],it[lt]=null,Dt=it[--lt],it[lt]=null,Nt=it[--lt],it[lt]=null}var Ze=null,Ye=null,ce=!1,gt=null;function Ed(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Pc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ze=e,Ye=rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ze=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pn!==null?{id:Nt,overflow:Dt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ze=e,Ye=null,!0):!1;default:return!1}}function Ms(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bs(e){if(ce){var t=Ye;if(t){var n=t;if(!Pc(e,t)){if(Ms(e))throw Error(L(418));t=rn(n.nextSibling);var r=Ze;t&&Pc(e,t)?Ed(r,n):(e.flags=e.flags&-4097|2,ce=!1,Ze=e)}}else{if(Ms(e))throw Error(L(418));e.flags=e.flags&-4097|2,ce=!1,Ze=e}}}function _c(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ze=e}function Bi(e){if(e!==Ze)return!1;if(!ce)return _c(e),ce=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!js(e.type,e.memoizedProps)),t&&(t=Ye)){if(Ms(e))throw bd(),Error(L(418));for(;t;)Ed(e,t),t=rn(t.nextSibling)}if(_c(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=Ze?rn(e.stateNode.nextSibling):null;return!0}function bd(){for(var e=Ye;e;)e=rn(e.nextSibling)}function cr(){Ye=Ze=null,ce=!1}function Gu(e){gt===null?gt=[e]:gt.push(e)}var sx=$t.ReactCurrentBatchConfig;function Ar(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var i=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(o){var s=i.refs;o===null?delete s[l]:s[l]=o},t._stringRef=l,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function Ui(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ac(e){var t=e._init;return t(e._payload)}function Td(e){function t(m,g){if(e){var y=m.deletions;y===null?(m.deletions=[g],m.flags|=16):y.push(g)}}function n(m,g){if(!e)return null;for(;g!==null;)t(m,g),g=g.sibling;return null}function r(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function i(m,g){return m=un(m,g),m.index=0,m.sibling=null,m}function l(m,g,y){return m.index=y,e?(y=m.alternate,y!==null?(y=y.index,y<g?(m.flags|=2,g):y):(m.flags|=2,g)):(m.flags|=1048576,g)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,g,y,v){return g===null||g.tag!==6?(g=Wo(y,m.mode,v),g.return=m,g):(g=i(g,y),g.return=m,g)}function u(m,g,y,v){var T=y.type;return T===Vn?c(m,g,y.props.children,v,y.key):g!==null&&(g.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Gt&&Ac(T)===g.type)?(v=i(g,y.props),v.ref=Ar(m,g,y),v.return=m,v):(v=sl(y.type,y.key,y.props,null,m.mode,v),v.ref=Ar(m,g,y),v.return=m,v)}function a(m,g,y,v){return g===null||g.tag!==4||g.stateNode.containerInfo!==y.containerInfo||g.stateNode.implementation!==y.implementation?(g=qo(y,m.mode,v),g.return=m,g):(g=i(g,y.children||[]),g.return=m,g)}function c(m,g,y,v,T){return g===null||g.tag!==7?(g=bn(y,m.mode,v,T),g.return=m,g):(g=i(g,y),g.return=m,g)}function f(m,g,y){if(typeof g=="string"&&g!==""||typeof g=="number")return g=Wo(""+g,m.mode,y),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Ai:return y=sl(g.type,g.key,g.props,null,m.mode,y),y.ref=Ar(m,null,g),y.return=m,y;case $n:return g=qo(g,m.mode,y),g.return=m,g;case Gt:var v=g._init;return f(m,v(g._payload),y)}if(Dr(g)||br(g))return g=bn(g,m.mode,y,null),g.return=m,g;Ui(m,g)}return null}function p(m,g,y,v){var T=g!==null?g.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return T!==null?null:s(m,g,""+y,v);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Ai:return y.key===T?u(m,g,y,v):null;case $n:return y.key===T?a(m,g,y,v):null;case Gt:return T=y._init,p(m,g,T(y._payload),v)}if(Dr(y)||br(y))return T!==null?null:c(m,g,y,v,null);Ui(m,y)}return null}function d(m,g,y,v,T){if(typeof v=="string"&&v!==""||typeof v=="number")return m=m.get(y)||null,s(g,m,""+v,T);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Ai:return m=m.get(v.key===null?y:v.key)||null,u(g,m,v,T);case $n:return m=m.get(v.key===null?y:v.key)||null,a(g,m,v,T);case Gt:var C=v._init;return d(m,g,y,C(v._payload),T)}if(Dr(v)||br(v))return m=m.get(y)||null,c(g,m,v,T,null);Ui(g,v)}return null}function h(m,g,y,v){for(var T=null,C=null,_=g,A=g=0,N=null;_!==null&&A<y.length;A++){_.index>A?(N=_,_=null):N=_.sibling;var b=p(m,_,y[A],v);if(b===null){_===null&&(_=N);break}e&&_&&b.alternate===null&&t(m,_),g=l(b,g,A),C===null?T=b:C.sibling=b,C=b,_=N}if(A===y.length)return n(m,_),ce&&yn(m,A),T;if(_===null){for(;A<y.length;A++)_=f(m,y[A],v),_!==null&&(g=l(_,g,A),C===null?T=_:C.sibling=_,C=_);return ce&&yn(m,A),T}for(_=r(m,_);A<y.length;A++)N=d(_,m,A,y[A],v),N!==null&&(e&&N.alternate!==null&&_.delete(N.key===null?A:N.key),g=l(N,g,A),C===null?T=N:C.sibling=N,C=N);return e&&_.forEach(function(j){return t(m,j)}),ce&&yn(m,A),T}function x(m,g,y,v){var T=br(y);if(typeof T!="function")throw Error(L(150));if(y=T.call(y),y==null)throw Error(L(151));for(var C=T=null,_=g,A=g=0,N=null,b=y.next();_!==null&&!b.done;A++,b=y.next()){_.index>A?(N=_,_=null):N=_.sibling;var j=p(m,_,b.value,v);if(j===null){_===null&&(_=N);break}e&&_&&j.alternate===null&&t(m,_),g=l(j,g,A),C===null?T=j:C.sibling=j,C=j,_=N}if(b.done)return n(m,_),ce&&yn(m,A),T;if(_===null){for(;!b.done;A++,b=y.next())b=f(m,b.value,v),b!==null&&(g=l(b,g,A),C===null?T=b:C.sibling=b,C=b);return ce&&yn(m,A),T}for(_=r(m,_);!b.done;A++,b=y.next())b=d(_,m,A,b.value,v),b!==null&&(e&&b.alternate!==null&&_.delete(b.key===null?A:b.key),g=l(b,g,A),C===null?T=b:C.sibling=b,C=b);return e&&_.forEach(function(F){return t(m,F)}),ce&&yn(m,A),T}function E(m,g,y,v){if(typeof y=="object"&&y!==null&&y.type===Vn&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Ai:e:{for(var T=y.key,C=g;C!==null;){if(C.key===T){if(T=y.type,T===Vn){if(C.tag===7){n(m,C.sibling),g=i(C,y.props.children),g.return=m,m=g;break e}}else if(C.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Gt&&Ac(T)===C.type){n(m,C.sibling),g=i(C,y.props),g.ref=Ar(m,C,y),g.return=m,m=g;break e}n(m,C);break}else t(m,C);C=C.sibling}y.type===Vn?(g=bn(y.props.children,m.mode,v,y.key),g.return=m,m=g):(v=sl(y.type,y.key,y.props,null,m.mode,v),v.ref=Ar(m,g,y),v.return=m,m=v)}return o(m);case $n:e:{for(C=y.key;g!==null;){if(g.key===C)if(g.tag===4&&g.stateNode.containerInfo===y.containerInfo&&g.stateNode.implementation===y.implementation){n(m,g.sibling),g=i(g,y.children||[]),g.return=m,m=g;break e}else{n(m,g);break}else t(m,g);g=g.sibling}g=qo(y,m.mode,v),g.return=m,m=g}return o(m);case Gt:return C=y._init,E(m,g,C(y._payload),v)}if(Dr(y))return h(m,g,y,v);if(br(y))return x(m,g,y,v);Ui(m,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,g!==null&&g.tag===6?(n(m,g.sibling),g=i(g,y),g.return=m,m=g):(n(m,g),g=Wo(y,m.mode,v),g.return=m,m=g),o(m)):n(m,g)}return E}var fr=Td(!0),Rd=Td(!1),Rl=pn(null),Pl=null,Yn=null,Ku=null;function Xu(){Ku=Yn=Pl=null}function Ju(e){var t=Rl.current;ue(Rl),e._currentValue=t}function Us(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lr(e,t){Pl=e,Ku=Yn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(He=!0),e.firstContext=null)}function at(e){var t=e._currentValue;if(Ku!==e)if(e={context:e,memoizedValue:t,next:null},Yn===null){if(Pl===null)throw Error(L(308));Yn=e,Pl.dependencies={lanes:0,firstContext:e}}else Yn=Yn.next=e;return t}var vn=null;function Yu(e){vn===null?vn=[e]:vn.push(e)}function Pd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Yu(t)):(n.next=i.next,i.next=n),t.interleaved=n,Ut(e,r)}function Ut(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Kt=!1;function Zu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _d(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ln(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Y&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Ut(e,n)}return i=r.interleaved,i===null?(t.next=t,Yu(r)):(t.next=i.next,i.next=t),r.interleaved=t,Ut(e,n)}function tl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fu(e,n)}}function Lc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?i=l=o:l=l.next=o,n=n.next}while(n!==null);l===null?i=l=t:l=l.next=t}else i=l=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function _l(e,t,n,r){var i=e.updateQueue;Kt=!1;var l=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var u=s,a=u.next;u.next=null,o===null?l=a:o.next=a,o=u;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==o&&(s===null?c.firstBaseUpdate=a:s.next=a,c.lastBaseUpdate=u))}if(l!==null){var f=i.baseState;o=0,c=a=u=null,s=l;do{var p=s.lane,d=s.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:d,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,x=s;switch(p=t,d=n,x.tag){case 1:if(h=x.payload,typeof h=="function"){f=h.call(d,f,p);break e}f=h;break e;case 3:h.flags=h.flags&-65537|128;case 0:if(h=x.payload,p=typeof h=="function"?h.call(d,f,p):h,p==null)break e;f=de({},f,p);break e;case 2:Kt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[s]:p.push(s))}else d={eventTime:d,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(a=c=d,u=f):c=c.next=d,o|=p;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;p=s,s=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(c===null&&(u=f),i.baseState=u,i.firstBaseUpdate=a,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else l===null&&(i.shared.lanes=0);An|=o,e.lanes=o,e.memoizedState=f}}function Ic(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(L(191,i));i.call(r)}}}var Si={},At=pn(Si),ai=pn(Si),ci=pn(Si);function Sn(e){if(e===Si)throw Error(L(174));return e}function ea(e,t){switch(le(ci,t),le(ai,e),le(At,Si),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:vs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=vs(t,e)}ue(At),le(At,t)}function pr(){ue(At),ue(ai),ue(ci)}function Ad(e){Sn(ci.current);var t=Sn(At.current),n=vs(t,e.type);t!==n&&(le(ai,e),le(At,n))}function ta(e){ai.current===e&&(ue(At),ue(ai))}var fe=pn(0);function Al(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Mo=[];function na(){for(var e=0;e<Mo.length;e++)Mo[e]._workInProgressVersionPrimary=null;Mo.length=0}var nl=$t.ReactCurrentDispatcher,Bo=$t.ReactCurrentBatchConfig,_n=0,pe=null,ve=null,Ce=null,Ll=!1,Wr=!1,fi=0,ux=0;function Pe(){throw Error(L(321))}function ra(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!wt(e[n],t[n]))return!1;return!0}function ia(e,t,n,r,i,l){if(_n=l,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nl.current=e===null||e.memoizedState===null?px:dx,e=n(r,i),Wr){l=0;do{if(Wr=!1,fi=0,25<=l)throw Error(L(301));l+=1,Ce=ve=null,t.updateQueue=null,nl.current=hx,e=n(r,i)}while(Wr)}if(nl.current=Il,t=ve!==null&&ve.next!==null,_n=0,Ce=ve=pe=null,Ll=!1,t)throw Error(L(300));return e}function la(){var e=fi!==0;return fi=0,e}function bt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?pe.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function ct(){if(ve===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=Ce===null?pe.memoizedState:Ce.next;if(t!==null)Ce=t,ve=e;else{if(e===null)throw Error(L(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},Ce===null?pe.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function pi(e,t){return typeof t=="function"?t(e):t}function Uo(e){var t=ct(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=ve,i=r.baseQueue,l=n.pending;if(l!==null){if(i!==null){var o=i.next;i.next=l.next,l.next=o}r.baseQueue=i=l,n.pending=null}if(i!==null){l=i.next,r=r.baseState;var s=o=null,u=null,a=l;do{var c=a.lane;if((_n&c)===c)u!==null&&(u=u.next={lane:0,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null}),r=a.hasEagerState?a.eagerState:e(r,a.action);else{var f={lane:c,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null};u===null?(s=u=f,o=r):u=u.next=f,pe.lanes|=c,An|=c}a=a.next}while(a!==null&&a!==l);u===null?o=r:u.next=s,wt(r,t.memoizedState)||(He=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do l=i.lane,pe.lanes|=l,An|=l,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ho(e){var t=ct(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,l=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do l=e(l,o.action),o=o.next;while(o!==i);wt(l,t.memoizedState)||(He=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Ld(){}function Id(e,t){var n=pe,r=ct(),i=t(),l=!wt(r.memoizedState,i);if(l&&(r.memoizedState=i,He=!0),r=r.queue,oa(jd.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||Ce!==null&&Ce.memoizedState.tag&1){if(n.flags|=2048,di(9,Od.bind(null,n,r,i,t),void 0,null),Ee===null)throw Error(L(349));_n&30||zd(n,t,i)}return i}function zd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Od(e,t,n,r){t.value=n,t.getSnapshot=r,Nd(t)&&Dd(e)}function jd(e,t,n){return n(function(){Nd(t)&&Dd(e)})}function Nd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!wt(e,n)}catch{return!0}}function Dd(e){var t=Ut(e,1);t!==null&&xt(t,e,1,-1)}function zc(e){var t=bt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:pi,lastRenderedState:e},t.queue=e,e=e.dispatch=fx.bind(null,pe,e),[t.memoizedState,e]}function di(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Fd(){return ct().memoizedState}function rl(e,t,n,r){var i=bt();pe.flags|=e,i.memoizedState=di(1|t,n,void 0,r===void 0?null:r)}function Yl(e,t,n,r){var i=ct();r=r===void 0?null:r;var l=void 0;if(ve!==null){var o=ve.memoizedState;if(l=o.destroy,r!==null&&ra(r,o.deps)){i.memoizedState=di(t,n,l,r);return}}pe.flags|=e,i.memoizedState=di(1|t,n,l,r)}function Oc(e,t){return rl(8390656,8,e,t)}function oa(e,t){return Yl(2048,8,e,t)}function Md(e,t){return Yl(4,2,e,t)}function Bd(e,t){return Yl(4,4,e,t)}function Ud(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Hd(e,t,n){return n=n!=null?n.concat([e]):null,Yl(4,4,Ud.bind(null,t,e),n)}function sa(){}function $d(e,t){var n=ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ra(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vd(e,t){var n=ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ra(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wd(e,t,n){return _n&21?(wt(n,t)||(n=Xp(),pe.lanes|=n,An|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,He=!0),e.memoizedState=n)}function ax(e,t){var n=Z;Z=n!==0&&4>n?n:4,e(!0);var r=Bo.transition;Bo.transition={};try{e(!1),t()}finally{Z=n,Bo.transition=r}}function qd(){return ct().memoizedState}function cx(e,t,n){var r=sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Qd(e))Gd(t,n);else if(n=Pd(e,t,n,r),n!==null){var i=De();xt(n,e,r,i),Kd(n,t,r)}}function fx(e,t,n){var r=sn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qd(e))Gd(t,i);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var o=t.lastRenderedState,s=l(o,n);if(i.hasEagerState=!0,i.eagerState=s,wt(s,o)){var u=t.interleaved;u===null?(i.next=i,Yu(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=Pd(e,t,i,r),n!==null&&(i=De(),xt(n,e,r,i),Kd(n,t,r))}}function Qd(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Gd(e,t){Wr=Ll=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fu(e,n)}}var Il={readContext:at,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useInsertionEffect:Pe,useLayoutEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useMutableSource:Pe,useSyncExternalStore:Pe,useId:Pe,unstable_isNewReconciler:!1},px={readContext:at,useCallback:function(e,t){return bt().memoizedState=[e,t===void 0?null:t],e},useContext:at,useEffect:Oc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,rl(4194308,4,Ud.bind(null,t,e),n)},useLayoutEffect:function(e,t){return rl(4194308,4,e,t)},useInsertionEffect:function(e,t){return rl(4,2,e,t)},useMemo:function(e,t){var n=bt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=cx.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=bt();return e={current:e},t.memoizedState=e},useState:zc,useDebugValue:sa,useDeferredValue:function(e){return bt().memoizedState=e},useTransition:function(){var e=zc(!1),t=e[0];return e=ax.bind(null,e[1]),bt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,i=bt();if(ce){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),Ee===null)throw Error(L(349));_n&30||zd(r,t,n)}i.memoizedState=n;var l={value:n,getSnapshot:t};return i.queue=l,Oc(jd.bind(null,r,l,e),[e]),r.flags|=2048,di(9,Od.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=bt(),t=Ee.identifierPrefix;if(ce){var n=Dt,r=Nt;n=(r&~(1<<32-yt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=fi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ux++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},dx={readContext:at,useCallback:$d,useContext:at,useEffect:oa,useImperativeHandle:Hd,useInsertionEffect:Md,useLayoutEffect:Bd,useMemo:Vd,useReducer:Uo,useRef:Fd,useState:function(){return Uo(pi)},useDebugValue:sa,useDeferredValue:function(e){var t=ct();return Wd(t,ve.memoizedState,e)},useTransition:function(){var e=Uo(pi)[0],t=ct().memoizedState;return[e,t]},useMutableSource:Ld,useSyncExternalStore:Id,useId:qd,unstable_isNewReconciler:!1},hx={readContext:at,useCallback:$d,useContext:at,useEffect:oa,useImperativeHandle:Hd,useInsertionEffect:Md,useLayoutEffect:Bd,useMemo:Vd,useReducer:Ho,useRef:Fd,useState:function(){return Ho(pi)},useDebugValue:sa,useDeferredValue:function(e){var t=ct();return ve===null?t.memoizedState=e:Wd(t,ve.memoizedState,e)},useTransition:function(){var e=Ho(pi)[0],t=ct().memoizedState;return[e,t]},useMutableSource:Ld,useSyncExternalStore:Id,useId:qd,unstable_isNewReconciler:!1};function ht(e,t){if(e&&e.defaultProps){t=de({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Hs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:de({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Zl={isMounted:function(e){return(e=e._reactInternals)?jn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=De(),i=sn(e),l=Ft(r,i);l.payload=t,n!=null&&(l.callback=n),t=ln(e,l,i),t!==null&&(xt(t,e,i,r),tl(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=De(),i=sn(e),l=Ft(r,i);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=ln(e,l,i),t!==null&&(xt(t,e,i,r),tl(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=De(),r=sn(e),i=Ft(n,r);i.tag=2,t!=null&&(i.callback=t),t=ln(e,i,r),t!==null&&(xt(t,e,r,n),tl(t,e,r))}};function jc(e,t,n,r,i,l,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,o):t.prototype&&t.prototype.isPureReactComponent?!li(n,r)||!li(i,l):!0}function Xd(e,t,n){var r=!1,i=cn,l=t.contextType;return typeof l=="object"&&l!==null?l=at(l):(i=Ve(t)?Rn:ze.current,r=t.contextTypes,l=(r=r!=null)?ar(e,i):cn),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Zl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=l),t}function Nc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Zl.enqueueReplaceState(t,t.state,null)}function $s(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Zu(e);var l=t.contextType;typeof l=="object"&&l!==null?i.context=at(l):(l=Ve(t)?Rn:ze.current,i.context=ar(e,l)),i.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(Hs(e,t,l,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Zl.enqueueReplaceState(i,i.state,null),_l(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function dr(e,t){try{var n="",r=t;do n+=Hg(r),r=r.return;while(r);var i=n}catch(l){i=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:i,digest:null}}function $o(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Vs(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var mx=typeof WeakMap=="function"?WeakMap:Map;function Jd(e,t,n){n=Ft(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ol||(Ol=!0,eu=r),Vs(e,t)},n}function Yd(e,t,n){n=Ft(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Vs(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Vs(e,t),typeof r!="function"&&(on===null?on=new Set([this]):on.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Dc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new mx;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=_x.bind(null,e,t,n),t.then(e,e))}function Fc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Mc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ft(-1,1),t.tag=2,ln(n,t,1))),n.lanes|=1),e)}var gx=$t.ReactCurrentOwner,He=!1;function je(e,t,n,r){t.child=e===null?Rd(t,null,n,r):fr(t,e.child,n,r)}function Bc(e,t,n,r,i){n=n.render;var l=t.ref;return lr(t,i),r=ia(e,t,n,r,l,i),n=la(),e!==null&&!He?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ht(e,t,i)):(ce&&n&&qu(t),t.flags|=1,je(e,t,r,i),t.child)}function Uc(e,t,n,r,i){if(e===null){var l=n.type;return typeof l=="function"&&!ma(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,Zd(e,t,l,r,i)):(e=sl(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&i)){var o=l.memoizedProps;if(n=n.compare,n=n!==null?n:li,n(o,r)&&e.ref===t.ref)return Ht(e,t,i)}return t.flags|=1,e=un(l,r),e.ref=t.ref,e.return=t,t.child=e}function Zd(e,t,n,r,i){if(e!==null){var l=e.memoizedProps;if(li(l,r)&&e.ref===t.ref)if(He=!1,t.pendingProps=r=l,(e.lanes&i)!==0)e.flags&131072&&(He=!0);else return t.lanes=e.lanes,Ht(e,t,i)}return Ws(e,t,n,r,i)}function eh(e,t,n){var r=t.pendingProps,i=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},le(er,Je),Je|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,le(er,Je),Je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,le(er,Je),Je|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,le(er,Je),Je|=r;return je(e,t,i,n),t.child}function th(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ws(e,t,n,r,i){var l=Ve(n)?Rn:ze.current;return l=ar(t,l),lr(t,i),n=ia(e,t,n,r,l,i),r=la(),e!==null&&!He?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ht(e,t,i)):(ce&&r&&qu(t),t.flags|=1,je(e,t,n,i),t.child)}function Hc(e,t,n,r,i){if(Ve(n)){var l=!0;El(t)}else l=!1;if(lr(t,i),t.stateNode===null)il(e,t),Xd(t,n,r),$s(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,s=t.memoizedProps;o.props=s;var u=o.context,a=n.contextType;typeof a=="object"&&a!==null?a=at(a):(a=Ve(n)?Rn:ze.current,a=ar(t,a));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==r||u!==a)&&Nc(t,o,r,a),Kt=!1;var p=t.memoizedState;o.state=p,_l(t,r,o,i),u=t.memoizedState,s!==r||p!==u||$e.current||Kt?(typeof c=="function"&&(Hs(t,n,c,r),u=t.memoizedState),(s=Kt||jc(t,n,s,r,p,u,a))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=a,r=s):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,_d(e,t),s=t.memoizedProps,a=t.type===t.elementType?s:ht(t.type,s),o.props=a,f=t.pendingProps,p=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=at(u):(u=Ve(n)?Rn:ze.current,u=ar(t,u));var d=n.getDerivedStateFromProps;(c=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==f||p!==u)&&Nc(t,o,r,u),Kt=!1,p=t.memoizedState,o.state=p,_l(t,r,o,i);var h=t.memoizedState;s!==f||p!==h||$e.current||Kt?(typeof d=="function"&&(Hs(t,n,d,r),h=t.memoizedState),(a=Kt||jc(t,n,a,r,p,h,u)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,h,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,h,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=u,r=a):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return qs(e,t,n,r,l,i)}function qs(e,t,n,r,i,l){th(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Rc(t,n,!1),Ht(e,t,l);r=t.stateNode,gx.current=t;var s=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=fr(t,e.child,null,l),t.child=fr(t,null,s,l)):je(e,t,s,l),t.memoizedState=r.state,i&&Rc(t,n,!0),t.child}function nh(e){var t=e.stateNode;t.pendingContext?Tc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Tc(e,t.context,!1),ea(e,t.containerInfo)}function $c(e,t,n,r,i){return cr(),Gu(i),t.flags|=256,je(e,t,n,r),t.child}var Qs={dehydrated:null,treeContext:null,retryLane:0};function Gs(e){return{baseLanes:e,cachePool:null,transitions:null}}function rh(e,t,n){var r=t.pendingProps,i=fe.current,l=!1,o=(t.flags&128)!==0,s;if((s=o)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),le(fe,i&1),e===null)return Bs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,l?(r=t.mode,l=t.child,o={mode:"hidden",children:o},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=o):l=no(o,r,0,null),e=bn(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Gs(n),t.memoizedState=Qs,e):ua(t,o));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return yx(e,t,o,r,s,i,n);if(l){l=r.fallback,o=t.mode,i=e.child,s=i.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=un(i,u),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?l=un(s,l):(l=bn(l,o,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,o=e.child.memoizedState,o=o===null?Gs(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},l.memoizedState=o,l.childLanes=e.childLanes&~n,t.memoizedState=Qs,r}return l=e.child,e=l.sibling,r=un(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ua(e,t){return t=no({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Hi(e,t,n,r){return r!==null&&Gu(r),fr(t,e.child,null,n),e=ua(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function yx(e,t,n,r,i,l,o){if(n)return t.flags&256?(t.flags&=-257,r=$o(Error(L(422))),Hi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,i=t.mode,r=no({mode:"visible",children:r.children},i,0,null),l=bn(l,i,o,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&fr(t,e.child,null,o),t.child.memoizedState=Gs(o),t.memoizedState=Qs,l);if(!(t.mode&1))return Hi(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,l=Error(L(419)),r=$o(l,r,void 0),Hi(e,t,o,r)}if(s=(o&e.childLanes)!==0,He||s){if(r=Ee,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==l.retryLane&&(l.retryLane=i,Ut(e,i),xt(r,e,i,-1))}return ha(),r=$o(Error(L(421))),Hi(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Ax.bind(null,e),i._reactRetry=t,null):(e=l.treeContext,Ye=rn(i.nextSibling),Ze=t,ce=!0,gt=null,e!==null&&(it[lt++]=Nt,it[lt++]=Dt,it[lt++]=Pn,Nt=e.id,Dt=e.overflow,Pn=t),t=ua(t,r.children),t.flags|=4096,t)}function Vc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Us(e.return,t,n)}function Vo(e,t,n,r,i){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=i)}function ih(e,t,n){var r=t.pendingProps,i=r.revealOrder,l=r.tail;if(je(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vc(e,n,t);else if(e.tag===19)Vc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(le(fe,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Al(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Vo(t,!1,i,n,l);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Al(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Vo(t,!0,n,null,l);break;case"together":Vo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function il(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),An|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=un(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=un(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xx(e,t,n){switch(t.tag){case 3:nh(t),cr();break;case 5:Ad(t);break;case 1:Ve(t.type)&&El(t);break;case 4:ea(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;le(Rl,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(le(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?rh(e,t,n):(le(fe,fe.current&1),e=Ht(e,t,n),e!==null?e.sibling:null);le(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ih(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),le(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,eh(e,t,n)}return Ht(e,t,n)}var lh,Ks,oh,sh;lh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ks=function(){};oh=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Sn(At.current);var l=null;switch(n){case"input":i=ys(e,i),r=ys(e,r),l=[];break;case"select":i=de({},i,{value:void 0}),r=de({},r,{value:void 0}),l=[];break;case"textarea":i=ws(e,i),r=ws(e,r),l=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Sl)}Ss(n,r);var o;n=null;for(a in i)if(!r.hasOwnProperty(a)&&i.hasOwnProperty(a)&&i[a]!=null)if(a==="style"){var s=i[a];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else a!=="dangerouslySetInnerHTML"&&a!=="children"&&a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Yr.hasOwnProperty(a)?l||(l=[]):(l=l||[]).push(a,null));for(a in r){var u=r[a];if(s=i!=null?i[a]:void 0,r.hasOwnProperty(a)&&u!==s&&(u!=null||s!=null))if(a==="style")if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(a,n)),n=u;else a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,s=s?s.__html:void 0,u!=null&&s!==u&&(l=l||[]).push(a,u)):a==="children"?typeof u!="string"&&typeof u!="number"||(l=l||[]).push(a,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&(Yr.hasOwnProperty(a)?(u!=null&&a==="onScroll"&&se("scroll",e),l||s===u||(l=[])):(l=l||[]).push(a,u))}n&&(l=l||[]).push("style",n);var a=l;(t.updateQueue=a)&&(t.flags|=4)}};sh=function(e,t,n,r){n!==r&&(t.flags|=4)};function Lr(e,t){if(!ce)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function _e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function kx(e,t,n){var r=t.pendingProps;switch(Qu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _e(t),null;case 1:return Ve(t.type)&&Cl(),_e(t),null;case 3:return r=t.stateNode,pr(),ue($e),ue(ze),na(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Bi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,gt!==null&&(ru(gt),gt=null))),Ks(e,t),_e(t),null;case 5:ta(t);var i=Sn(ci.current);if(n=t.type,e!==null&&t.stateNode!=null)oh(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return _e(t),null}if(e=Sn(At.current),Bi(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Rt]=t,r[ui]=l,e=(t.mode&1)!==0,n){case"dialog":se("cancel",r),se("close",r);break;case"iframe":case"object":case"embed":se("load",r);break;case"video":case"audio":for(i=0;i<Mr.length;i++)se(Mr[i],r);break;case"source":se("error",r);break;case"img":case"image":case"link":se("error",r),se("load",r);break;case"details":se("toggle",r);break;case"input":Za(r,l),se("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},se("invalid",r);break;case"textarea":tc(r,l),se("invalid",r)}Ss(n,l),i=null;for(var o in l)if(l.hasOwnProperty(o)){var s=l[o];o==="children"?typeof s=="string"?r.textContent!==s&&(l.suppressHydrationWarning!==!0&&Mi(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(l.suppressHydrationWarning!==!0&&Mi(r.textContent,s,e),i=["children",""+s]):Yr.hasOwnProperty(o)&&s!=null&&o==="onScroll"&&se("scroll",r)}switch(n){case"input":Li(r),ec(r,l,!0);break;case"textarea":Li(r),nc(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=Sl)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=jp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Rt]=t,e[ui]=r,lh(e,t,!1,!1),t.stateNode=e;e:{switch(o=Cs(n,r),n){case"dialog":se("cancel",e),se("close",e),i=r;break;case"iframe":case"object":case"embed":se("load",e),i=r;break;case"video":case"audio":for(i=0;i<Mr.length;i++)se(Mr[i],e);i=r;break;case"source":se("error",e),i=r;break;case"img":case"image":case"link":se("error",e),se("load",e),i=r;break;case"details":se("toggle",e),i=r;break;case"input":Za(e,r),i=ys(e,r),se("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=de({},r,{value:void 0}),se("invalid",e);break;case"textarea":tc(e,r),i=ws(e,r),se("invalid",e);break;default:i=r}Ss(n,i),s=i;for(l in s)if(s.hasOwnProperty(l)){var u=s[l];l==="style"?Fp(e,u):l==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Np(e,u)):l==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Zr(e,u):typeof u=="number"&&Zr(e,""+u):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Yr.hasOwnProperty(l)?u!=null&&l==="onScroll"&&se("scroll",e):u!=null&&Iu(e,l,u,o))}switch(n){case"input":Li(e),ec(e,r,!1);break;case"textarea":Li(e),nc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+an(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?tr(e,!!r.multiple,l,!1):r.defaultValue!=null&&tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Sl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return _e(t),null;case 6:if(e&&t.stateNode!=null)sh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=Sn(ci.current),Sn(At.current),Bi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Rt]=t,(l=r.nodeValue!==n)&&(e=Ze,e!==null))switch(e.tag){case 3:Mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Mi(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Rt]=t,t.stateNode=r}return _e(t),null;case 13:if(ue(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ce&&Ye!==null&&t.mode&1&&!(t.flags&128))bd(),cr(),t.flags|=98560,l=!1;else if(l=Bi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(L(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(L(317));l[Rt]=t}else cr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;_e(t),l=!1}else gt!==null&&(ru(gt),gt=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Se===0&&(Se=3):ha())),t.updateQueue!==null&&(t.flags|=4),_e(t),null);case 4:return pr(),Ks(e,t),e===null&&oi(t.stateNode.containerInfo),_e(t),null;case 10:return Ju(t.type._context),_e(t),null;case 17:return Ve(t.type)&&Cl(),_e(t),null;case 19:if(ue(fe),l=t.memoizedState,l===null)return _e(t),null;if(r=(t.flags&128)!==0,o=l.rendering,o===null)if(r)Lr(l,!1);else{if(Se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Al(e),o!==null){for(t.flags|=128,Lr(l,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,o=l.alternate,o===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=o.childLanes,l.lanes=o.lanes,l.child=o.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=o.memoizedProps,l.memoizedState=o.memoizedState,l.updateQueue=o.updateQueue,l.type=o.type,e=o.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return le(fe,fe.current&1|2),t.child}e=e.sibling}l.tail!==null&&ye()>hr&&(t.flags|=128,r=!0,Lr(l,!1),t.lanes=4194304)}else{if(!r)if(e=Al(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Lr(l,!0),l.tail===null&&l.tailMode==="hidden"&&!o.alternate&&!ce)return _e(t),null}else 2*ye()-l.renderingStartTime>hr&&n!==1073741824&&(t.flags|=128,r=!0,Lr(l,!1),t.lanes=4194304);l.isBackwards?(o.sibling=t.child,t.child=o):(n=l.last,n!==null?n.sibling=o:t.child=o,l.last=o)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=ye(),t.sibling=null,n=fe.current,le(fe,r?n&1|2:n&1),t):(_e(t),null);case 22:case 23:return da(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Je&1073741824&&(_e(t),t.subtreeFlags&6&&(t.flags|=8192)):_e(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function wx(e,t){switch(Qu(t),t.tag){case 1:return Ve(t.type)&&Cl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return pr(),ue($e),ue(ze),na(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ta(t),null;case 13:if(ue(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));cr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ue(fe),null;case 4:return pr(),null;case 10:return Ju(t.type._context),null;case 22:case 23:return da(),null;case 24:return null;default:return null}}var $i=!1,Ae=!1,vx=typeof WeakSet=="function"?WeakSet:Set,D=null;function Zn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function Xs(e,t,n){try{n()}catch(r){he(e,t,r)}}var Wc=!1;function Sx(e,t){if(zs=kl,e=pd(),Wu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var o=0,s=-1,u=-1,a=0,c=0,f=e,p=null;t:for(;;){for(var d;f!==n||i!==0&&f.nodeType!==3||(s=o+i),f!==l||r!==0&&f.nodeType!==3||(u=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(d=f.firstChild)!==null;)p=f,f=d;for(;;){if(f===e)break t;if(p===n&&++a===i&&(s=o),p===l&&++c===r&&(u=o),(d=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=d}n=s===-1||u===-1?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Os={focusedElem:e,selectionRange:n},kl=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var h=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(h!==null){var x=h.memoizedProps,E=h.memoizedState,m=t.stateNode,g=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:ht(t.type,x),E);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(v){he(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return h=Wc,Wc=!1,h}function qr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var l=i.destroy;i.destroy=void 0,l!==void 0&&Xs(t,n,l)}i=i.next}while(i!==r)}}function eo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Js(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function uh(e){var t=e.alternate;t!==null&&(e.alternate=null,uh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[ui],delete t[Ds],delete t[ix],delete t[lx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ah(e){return e.tag===5||e.tag===3||e.tag===4}function qc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ah(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ys(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Sl));else if(r!==4&&(e=e.child,e!==null))for(Ys(e,t,n),e=e.sibling;e!==null;)Ys(e,t,n),e=e.sibling}function Zs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Zs(e,t,n),e=e.sibling;e!==null;)Zs(e,t,n),e=e.sibling}var be=null,mt=!1;function qt(e,t,n){for(n=n.child;n!==null;)ch(e,t,n),n=n.sibling}function ch(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(ql,n)}catch{}switch(n.tag){case 5:Ae||Zn(n,t);case 6:var r=be,i=mt;be=null,qt(e,t,n),be=r,mt=i,be!==null&&(mt?(e=be,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):be.removeChild(n.stateNode));break;case 18:be!==null&&(mt?(e=be,n=n.stateNode,e.nodeType===8?Do(e.parentNode,n):e.nodeType===1&&Do(e,n),ri(e)):Do(be,n.stateNode));break;case 4:r=be,i=mt,be=n.stateNode.containerInfo,mt=!0,qt(e,t,n),be=r,mt=i;break;case 0:case 11:case 14:case 15:if(!Ae&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var l=i,o=l.destroy;l=l.tag,o!==void 0&&(l&2||l&4)&&Xs(n,t,o),i=i.next}while(i!==r)}qt(e,t,n);break;case 1:if(!Ae&&(Zn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){he(n,t,s)}qt(e,t,n);break;case 21:qt(e,t,n);break;case 22:n.mode&1?(Ae=(r=Ae)||n.memoizedState!==null,qt(e,t,n),Ae=r):qt(e,t,n);break;default:qt(e,t,n)}}function Qc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new vx),t.forEach(function(r){var i=Lx.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function dt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var l=e,o=t,s=o;e:for(;s!==null;){switch(s.tag){case 5:be=s.stateNode,mt=!1;break e;case 3:be=s.stateNode.containerInfo,mt=!0;break e;case 4:be=s.stateNode.containerInfo,mt=!0;break e}s=s.return}if(be===null)throw Error(L(160));ch(l,o,i),be=null,mt=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(a){he(i,t,a)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)fh(t,e),t=t.sibling}function fh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(dt(t,e),Ct(e),r&4){try{qr(3,e,e.return),eo(3,e)}catch(x){he(e,e.return,x)}try{qr(5,e,e.return)}catch(x){he(e,e.return,x)}}break;case 1:dt(t,e),Ct(e),r&512&&n!==null&&Zn(n,n.return);break;case 5:if(dt(t,e),Ct(e),r&512&&n!==null&&Zn(n,n.return),e.flags&32){var i=e.stateNode;try{Zr(i,"")}catch(x){he(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var l=e.memoizedProps,o=n!==null?n.memoizedProps:l,s=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{s==="input"&&l.type==="radio"&&l.name!=null&&zp(i,l),Cs(s,o);var a=Cs(s,l);for(o=0;o<u.length;o+=2){var c=u[o],f=u[o+1];c==="style"?Fp(i,f):c==="dangerouslySetInnerHTML"?Np(i,f):c==="children"?Zr(i,f):Iu(i,c,f,a)}switch(s){case"input":xs(i,l);break;case"textarea":Op(i,l);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!l.multiple;var d=l.value;d!=null?tr(i,!!l.multiple,d,!1):p!==!!l.multiple&&(l.defaultValue!=null?tr(i,!!l.multiple,l.defaultValue,!0):tr(i,!!l.multiple,l.multiple?[]:"",!1))}i[ui]=l}catch(x){he(e,e.return,x)}}break;case 6:if(dt(t,e),Ct(e),r&4){if(e.stateNode===null)throw Error(L(162));i=e.stateNode,l=e.memoizedProps;try{i.nodeValue=l}catch(x){he(e,e.return,x)}}break;case 3:if(dt(t,e),Ct(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ri(t.containerInfo)}catch(x){he(e,e.return,x)}break;case 4:dt(t,e),Ct(e);break;case 13:dt(t,e),Ct(e),i=e.child,i.flags&8192&&(l=i.memoizedState!==null,i.stateNode.isHidden=l,!l||i.alternate!==null&&i.alternate.memoizedState!==null||(fa=ye())),r&4&&Qc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ae=(a=Ae)||c,dt(t,e),Ae=a):dt(t,e),Ct(e),r&8192){if(a=e.memoizedState!==null,(e.stateNode.isHidden=a)&&!c&&e.mode&1)for(D=e,c=e.child;c!==null;){for(f=D=c;D!==null;){switch(p=D,d=p.child,p.tag){case 0:case 11:case 14:case 15:qr(4,p,p.return);break;case 1:Zn(p,p.return);var h=p.stateNode;if(typeof h.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(x){he(r,n,x)}}break;case 5:Zn(p,p.return);break;case 22:if(p.memoizedState!==null){Kc(f);continue}}d!==null?(d.return=p,D=d):Kc(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,a?(l=i.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(s=f.stateNode,u=f.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,s.style.display=Dp("display",o))}catch(x){he(e,e.return,x)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=a?"":f.memoizedProps}catch(x){he(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:dt(t,e),Ct(e),r&4&&Qc(e);break;case 21:break;default:dt(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ah(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Zr(i,""),r.flags&=-33);var l=qc(e);Zs(e,l,i);break;case 3:case 4:var o=r.stateNode.containerInfo,s=qc(e);Ys(e,s,o);break;default:throw Error(L(161))}}catch(u){he(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cx(e,t,n){D=e,ph(e)}function ph(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var i=D,l=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||$i;if(!o){var s=i.alternate,u=s!==null&&s.memoizedState!==null||Ae;s=$i;var a=Ae;if($i=o,(Ae=u)&&!a)for(D=i;D!==null;)o=D,u=o.child,o.tag===22&&o.memoizedState!==null?Xc(i):u!==null?(u.return=o,D=u):Xc(i);for(;l!==null;)D=l,ph(l),l=l.sibling;D=i,$i=s,Ae=a}Gc(e)}else i.subtreeFlags&8772&&l!==null?(l.return=i,D=l):Gc(e)}}function Gc(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ae||eo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ae)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:ht(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&Ic(t,l,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ic(t,o,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var a=t.alternate;if(a!==null){var c=a.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&ri(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}Ae||t.flags&512&&Js(t)}catch(p){he(t,t.return,p)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function Kc(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function Xc(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{eo(4,t)}catch(u){he(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){he(t,i,u)}}var l=t.return;try{Js(t)}catch(u){he(t,l,u)}break;case 5:var o=t.return;try{Js(t)}catch(u){he(t,o,u)}}}catch(u){he(t,t.return,u)}if(t===e){D=null;break}var s=t.sibling;if(s!==null){s.return=t.return,D=s;break}D=t.return}}var Ex=Math.ceil,zl=$t.ReactCurrentDispatcher,aa=$t.ReactCurrentOwner,ut=$t.ReactCurrentBatchConfig,Y=0,Ee=null,ke=null,Te=0,Je=0,er=pn(0),Se=0,hi=null,An=0,to=0,ca=0,Qr=null,Ue=null,fa=0,hr=1/0,Ot=null,Ol=!1,eu=null,on=null,Vi=!1,Zt=null,jl=0,Gr=0,tu=null,ll=-1,ol=0;function De(){return Y&6?ye():ll!==-1?ll:ll=ye()}function sn(e){return e.mode&1?Y&2&&Te!==0?Te&-Te:sx.transition!==null?(ol===0&&(ol=Xp()),ol):(e=Z,e!==0||(e=window.event,e=e===void 0?16:rd(e.type)),e):1}function xt(e,t,n,r){if(50<Gr)throw Gr=0,tu=null,Error(L(185));ki(e,n,r),(!(Y&2)||e!==Ee)&&(e===Ee&&(!(Y&2)&&(to|=n),Se===4&&Jt(e,Te)),We(e,r),n===1&&Y===0&&!(t.mode&1)&&(hr=ye()+500,Jl&&dn()))}function We(e,t){var n=e.callbackNode;sy(e,t);var r=xl(e,e===Ee?Te:0);if(r===0)n!==null&&lc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lc(n),t===1)e.tag===0?ox(Jc.bind(null,e)):Sd(Jc.bind(null,e)),nx(function(){!(Y&6)&&dn()}),n=null;else{switch(Jp(r)){case 1:n=Du;break;case 4:n=Gp;break;case 16:n=yl;break;case 536870912:n=Kp;break;default:n=yl}n=wh(n,dh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function dh(e,t){if(ll=-1,ol=0,Y&6)throw Error(L(327));var n=e.callbackNode;if(or()&&e.callbackNode!==n)return null;var r=xl(e,e===Ee?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Nl(e,r);else{t=r;var i=Y;Y|=2;var l=mh();(Ee!==e||Te!==t)&&(Ot=null,hr=ye()+500,En(e,t));do try{Rx();break}catch(s){hh(e,s)}while(!0);Xu(),zl.current=l,Y=i,ke!==null?t=0:(Ee=null,Te=0,t=Se)}if(t!==0){if(t===2&&(i=Ps(e),i!==0&&(r=i,t=nu(e,i))),t===1)throw n=hi,En(e,0),Jt(e,r),We(e,ye()),n;if(t===6)Jt(e,r);else{if(i=e.current.alternate,!(r&30)&&!bx(i)&&(t=Nl(e,r),t===2&&(l=Ps(e),l!==0&&(r=l,t=nu(e,l))),t===1))throw n=hi,En(e,0),Jt(e,r),We(e,ye()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:xn(e,Ue,Ot);break;case 3:if(Jt(e,r),(r&130023424)===r&&(t=fa+500-ye(),10<t)){if(xl(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){De(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Ns(xn.bind(null,e,Ue,Ot),t);break}xn(e,Ue,Ot);break;case 4:if(Jt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-yt(r);l=1<<o,o=t[o],o>i&&(i=o),r&=~l}if(r=i,r=ye()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ex(r/1960))-r,10<r){e.timeoutHandle=Ns(xn.bind(null,e,Ue,Ot),r);break}xn(e,Ue,Ot);break;case 5:xn(e,Ue,Ot);break;default:throw Error(L(329))}}}return We(e,ye()),e.callbackNode===n?dh.bind(null,e):null}function nu(e,t){var n=Qr;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=Nl(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&ru(t)),e}function ru(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function bx(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],l=i.getSnapshot;i=i.value;try{if(!wt(l(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Jt(e,t){for(t&=~ca,t&=~to,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-yt(t),r=1<<n;e[n]=-1,t&=~r}}function Jc(e){if(Y&6)throw Error(L(327));or();var t=xl(e,0);if(!(t&1))return We(e,ye()),null;var n=Nl(e,t);if(e.tag!==0&&n===2){var r=Ps(e);r!==0&&(t=r,n=nu(e,r))}if(n===1)throw n=hi,En(e,0),Jt(e,t),We(e,ye()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xn(e,Ue,Ot),We(e,ye()),null}function pa(e,t){var n=Y;Y|=1;try{return e(t)}finally{Y=n,Y===0&&(hr=ye()+500,Jl&&dn())}}function Ln(e){Zt!==null&&Zt.tag===0&&!(Y&6)&&or();var t=Y;Y|=1;var n=ut.transition,r=Z;try{if(ut.transition=null,Z=1,e)return e()}finally{Z=r,ut.transition=n,Y=t,!(Y&6)&&dn()}}function da(){Je=er.current,ue(er)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,tx(n)),ke!==null)for(n=ke.return;n!==null;){var r=n;switch(Qu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Cl();break;case 3:pr(),ue($e),ue(ze),na();break;case 5:ta(r);break;case 4:pr();break;case 13:ue(fe);break;case 19:ue(fe);break;case 10:Ju(r.type._context);break;case 22:case 23:da()}n=n.return}if(Ee=e,ke=e=un(e.current,null),Te=Je=t,Se=0,hi=null,ca=to=An=0,Ue=Qr=null,vn!==null){for(t=0;t<vn.length;t++)if(n=vn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,l=n.pending;if(l!==null){var o=l.next;l.next=i,r.next=o}n.pending=r}vn=null}return e}function hh(e,t){do{var n=ke;try{if(Xu(),nl.current=Il,Ll){for(var r=pe.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Ll=!1}if(_n=0,Ce=ve=pe=null,Wr=!1,fi=0,aa.current=null,n===null||n.return===null){Se=1,hi=t,ke=null;break}e:{var l=e,o=n.return,s=n,u=t;if(t=Te,s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var a=u,c=s,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var d=Fc(o);if(d!==null){d.flags&=-257,Mc(d,o,s,l,t),d.mode&1&&Dc(l,a,t),t=d,u=a;var h=t.updateQueue;if(h===null){var x=new Set;x.add(u),t.updateQueue=x}else h.add(u);break e}else{if(!(t&1)){Dc(l,a,t),ha();break e}u=Error(L(426))}}else if(ce&&s.mode&1){var E=Fc(o);if(E!==null){!(E.flags&65536)&&(E.flags|=256),Mc(E,o,s,l,t),Gu(dr(u,s));break e}}l=u=dr(u,s),Se!==4&&(Se=2),Qr===null?Qr=[l]:Qr.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var m=Jd(l,u,t);Lc(l,m);break e;case 1:s=u;var g=l.type,y=l.stateNode;if(!(l.flags&128)&&(typeof g.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(on===null||!on.has(y)))){l.flags|=65536,t&=-t,l.lanes|=t;var v=Yd(l,s,t);Lc(l,v);break e}}l=l.return}while(l!==null)}yh(n)}catch(T){t=T,ke===n&&n!==null&&(ke=n=n.return);continue}break}while(!0)}function mh(){var e=zl.current;return zl.current=Il,e===null?Il:e}function ha(){(Se===0||Se===3||Se===2)&&(Se=4),Ee===null||!(An&268435455)&&!(to&268435455)||Jt(Ee,Te)}function Nl(e,t){var n=Y;Y|=2;var r=mh();(Ee!==e||Te!==t)&&(Ot=null,En(e,t));do try{Tx();break}catch(i){hh(e,i)}while(!0);if(Xu(),Y=n,zl.current=r,ke!==null)throw Error(L(261));return Ee=null,Te=0,Se}function Tx(){for(;ke!==null;)gh(ke)}function Rx(){for(;ke!==null&&!Yg();)gh(ke)}function gh(e){var t=kh(e.alternate,e,Je);e.memoizedProps=e.pendingProps,t===null?yh(e):ke=t,aa.current=null}function yh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=wx(n,t),n!==null){n.flags&=32767,ke=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Se=6,ke=null;return}}else if(n=kx(n,t,Je),n!==null){ke=n;return}if(t=t.sibling,t!==null){ke=t;return}ke=t=e}while(t!==null);Se===0&&(Se=5)}function xn(e,t,n){var r=Z,i=ut.transition;try{ut.transition=null,Z=1,Px(e,t,n,r)}finally{ut.transition=i,Z=r}return null}function Px(e,t,n,r){do or();while(Zt!==null);if(Y&6)throw Error(L(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(uy(e,l),e===Ee&&(ke=Ee=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Vi||(Vi=!0,wh(yl,function(){return or(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=ut.transition,ut.transition=null;var o=Z;Z=1;var s=Y;Y|=4,aa.current=null,Sx(e,n),fh(n,e),Gy(Os),kl=!!zs,Os=zs=null,e.current=n,Cx(n),Zg(),Y=s,Z=o,ut.transition=l}else e.current=n;if(Vi&&(Vi=!1,Zt=e,jl=i),l=e.pendingLanes,l===0&&(on=null),ny(n.stateNode),We(e,ye()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ol)throw Ol=!1,e=eu,eu=null,e;return jl&1&&e.tag!==0&&or(),l=e.pendingLanes,l&1?e===tu?Gr++:(Gr=0,tu=e):Gr=0,dn(),null}function or(){if(Zt!==null){var e=Jp(jl),t=ut.transition,n=Z;try{if(ut.transition=null,Z=16>e?16:e,Zt===null)var r=!1;else{if(e=Zt,Zt=null,jl=0,Y&6)throw Error(L(331));var i=Y;for(Y|=4,D=e.current;D!==null;){var l=D,o=l.child;if(D.flags&16){var s=l.deletions;if(s!==null){for(var u=0;u<s.length;u++){var a=s[u];for(D=a;D!==null;){var c=D;switch(c.tag){case 0:case 11:case 15:qr(8,c,l)}var f=c.child;if(f!==null)f.return=c,D=f;else for(;D!==null;){c=D;var p=c.sibling,d=c.return;if(uh(c),c===a){D=null;break}if(p!==null){p.return=d,D=p;break}D=d}}}var h=l.alternate;if(h!==null){var x=h.child;if(x!==null){h.child=null;do{var E=x.sibling;x.sibling=null,x=E}while(x!==null)}}D=l}}if(l.subtreeFlags&2064&&o!==null)o.return=l,D=o;else e:for(;D!==null;){if(l=D,l.flags&2048)switch(l.tag){case 0:case 11:case 15:qr(9,l,l.return)}var m=l.sibling;if(m!==null){m.return=l.return,D=m;break e}D=l.return}}var g=e.current;for(D=g;D!==null;){o=D;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,D=y;else e:for(o=g;D!==null;){if(s=D,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:eo(9,s)}}catch(T){he(s,s.return,T)}if(s===o){D=null;break e}var v=s.sibling;if(v!==null){v.return=s.return,D=v;break e}D=s.return}}if(Y=i,dn(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(ql,e)}catch{}r=!0}return r}finally{Z=n,ut.transition=t}}return!1}function Yc(e,t,n){t=dr(n,t),t=Jd(e,t,1),e=ln(e,t,1),t=De(),e!==null&&(ki(e,1,t),We(e,t))}function he(e,t,n){if(e.tag===3)Yc(e,e,n);else for(;t!==null;){if(t.tag===3){Yc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(on===null||!on.has(r))){e=dr(n,e),e=Yd(t,e,1),t=ln(t,e,1),e=De(),t!==null&&(ki(t,1,e),We(t,e));break}}t=t.return}}function _x(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=De(),e.pingedLanes|=e.suspendedLanes&n,Ee===e&&(Te&n)===n&&(Se===4||Se===3&&(Te&130023424)===Te&&500>ye()-fa?En(e,0):ca|=n),We(e,t)}function xh(e,t){t===0&&(e.mode&1?(t=Oi,Oi<<=1,!(Oi&130023424)&&(Oi=4194304)):t=1);var n=De();e=Ut(e,t),e!==null&&(ki(e,t,n),We(e,n))}function Ax(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),xh(e,n)}function Lx(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),xh(e,n)}var kh;kh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||$e.current)He=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return He=!1,xx(e,t,n);He=!!(e.flags&131072)}else He=!1,ce&&t.flags&1048576&&Cd(t,Tl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;il(e,t),e=t.pendingProps;var i=ar(t,ze.current);lr(t,n),i=ia(null,t,r,e,i,n);var l=la();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ve(r)?(l=!0,El(t)):l=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Zu(t),i.updater=Zl,t.stateNode=i,i._reactInternals=t,$s(t,r,e,n),t=qs(null,t,r,!0,l,n)):(t.tag=0,ce&&l&&qu(t),je(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(il(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=zx(r),e=ht(r,e),i){case 0:t=Ws(null,t,r,e,n);break e;case 1:t=Hc(null,t,r,e,n);break e;case 11:t=Bc(null,t,r,e,n);break e;case 14:t=Uc(null,t,r,ht(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ht(r,i),Ws(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ht(r,i),Hc(e,t,r,i,n);case 3:e:{if(nh(t),e===null)throw Error(L(387));r=t.pendingProps,l=t.memoizedState,i=l.element,_d(e,t),_l(t,r,null,n);var o=t.memoizedState;if(r=o.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){i=dr(Error(L(423)),t),t=$c(e,t,r,n,i);break e}else if(r!==i){i=dr(Error(L(424)),t),t=$c(e,t,r,n,i);break e}else for(Ye=rn(t.stateNode.containerInfo.firstChild),Ze=t,ce=!0,gt=null,n=Rd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cr(),r===i){t=Ht(e,t,n);break e}je(e,t,r,n)}t=t.child}return t;case 5:return Ad(t),e===null&&Bs(t),r=t.type,i=t.pendingProps,l=e!==null?e.memoizedProps:null,o=i.children,js(r,i)?o=null:l!==null&&js(r,l)&&(t.flags|=32),th(e,t),je(e,t,o,n),t.child;case 6:return e===null&&Bs(t),null;case 13:return rh(e,t,n);case 4:return ea(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=fr(t,null,r,n):je(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ht(r,i),Bc(e,t,r,i,n);case 7:return je(e,t,t.pendingProps,n),t.child;case 8:return je(e,t,t.pendingProps.children,n),t.child;case 12:return je(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,l=t.memoizedProps,o=i.value,le(Rl,r._currentValue),r._currentValue=o,l!==null)if(wt(l.value,o)){if(l.children===i.children&&!$e.current){t=Ht(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var s=l.dependencies;if(s!==null){o=l.child;for(var u=s.firstContext;u!==null;){if(u.context===r){if(l.tag===1){u=Ft(-1,n&-n),u.tag=2;var a=l.updateQueue;if(a!==null){a=a.shared;var c=a.pending;c===null?u.next=u:(u.next=c.next,c.next=u),a.pending=u}}l.lanes|=n,u=l.alternate,u!==null&&(u.lanes|=n),Us(l.return,n,t),s.lanes|=n;break}u=u.next}}else if(l.tag===10)o=l.type===t.type?null:l.child;else if(l.tag===18){if(o=l.return,o===null)throw Error(L(341));o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),Us(o,n,t),o=l.sibling}else o=l.child;if(o!==null)o.return=l;else for(o=l;o!==null;){if(o===t){o=null;break}if(l=o.sibling,l!==null){l.return=o.return,o=l;break}o=o.return}l=o}je(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,lr(t,n),i=at(i),r=r(i),t.flags|=1,je(e,t,r,n),t.child;case 14:return r=t.type,i=ht(r,t.pendingProps),i=ht(r.type,i),Uc(e,t,r,i,n);case 15:return Zd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ht(r,i),il(e,t),t.tag=1,Ve(r)?(e=!0,El(t)):e=!1,lr(t,n),Xd(t,r,i),$s(t,r,i,n),qs(null,t,r,!0,e,n);case 19:return ih(e,t,n);case 22:return eh(e,t,n)}throw Error(L(156,t.tag))};function wh(e,t){return Qp(e,t)}function Ix(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new Ix(e,t,n,r)}function ma(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zx(e){if(typeof e=="function")return ma(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ou)return 11;if(e===ju)return 14}return 2}function un(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function sl(e,t,n,r,i,l){var o=2;if(r=e,typeof e=="function")ma(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Vn:return bn(n.children,i,l,t);case zu:o=8,i|=8;break;case ds:return e=st(12,n,t,i|2),e.elementType=ds,e.lanes=l,e;case hs:return e=st(13,n,t,i),e.elementType=hs,e.lanes=l,e;case ms:return e=st(19,n,t,i),e.elementType=ms,e.lanes=l,e;case Ap:return no(n,i,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Pp:o=10;break e;case _p:o=9;break e;case Ou:o=11;break e;case ju:o=14;break e;case Gt:o=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=st(o,n,t,i),t.elementType=e,t.type=r,t.lanes=l,t}function bn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function no(e,t,n,r){return e=st(22,e,r,t),e.elementType=Ap,e.lanes=n,e.stateNode={isHidden:!1},e}function Wo(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function qo(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ox(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=To(0),this.expirationTimes=To(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=To(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ga(e,t,n,r,i,l,o,s,u){return e=new Ox(e,t,n,s,u),t===1?(t=1,l===!0&&(t|=8)):t=0,l=st(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zu(l),e}function jx(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:$n,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function vh(e){if(!e)return cn;e=e._reactInternals;e:{if(jn(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(Ve(n))return vd(e,n,t)}return t}function Sh(e,t,n,r,i,l,o,s,u){return e=ga(n,r,!0,e,i,l,o,s,u),e.context=vh(null),n=e.current,r=De(),i=sn(n),l=Ft(r,i),l.callback=t??null,ln(n,l,i),e.current.lanes=i,ki(e,i,r),We(e,r),e}function ro(e,t,n,r){var i=t.current,l=De(),o=sn(i);return n=vh(n),t.context===null?t.context=n:t.pendingContext=n,t=Ft(l,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ln(i,t,o),e!==null&&(xt(e,i,o,l),tl(e,i,o)),o}function Dl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Zc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ya(e,t){Zc(e,t),(e=e.alternate)&&Zc(e,t)}function Nx(){return null}var Ch=typeof reportError=="function"?reportError:function(e){console.error(e)};function xa(e){this._internalRoot=e}io.prototype.render=xa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));ro(e,t,null,null)};io.prototype.unmount=xa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ln(function(){ro(null,e,null,null)}),t[Bt]=null}};function io(e){this._internalRoot=e}io.prototype.unstable_scheduleHydration=function(e){if(e){var t=ed();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Xt.length&&t!==0&&t<Xt[n].priority;n++);Xt.splice(n,0,e),n===0&&nd(e)}};function ka(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function lo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ef(){}function Dx(e,t,n,r,i){if(i){if(typeof r=="function"){var l=r;r=function(){var a=Dl(o);l.call(a)}}var o=Sh(t,r,e,0,null,!1,!1,"",ef);return e._reactRootContainer=o,e[Bt]=o.current,oi(e.nodeType===8?e.parentNode:e),Ln(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var a=Dl(u);s.call(a)}}var u=ga(e,0,!1,null,null,!1,!1,"",ef);return e._reactRootContainer=u,e[Bt]=u.current,oi(e.nodeType===8?e.parentNode:e),Ln(function(){ro(t,u,n,r)}),u}function oo(e,t,n,r,i){var l=n._reactRootContainer;if(l){var o=l;if(typeof i=="function"){var s=i;i=function(){var u=Dl(o);s.call(u)}}ro(t,o,e,i)}else o=Dx(n,t,e,i,r);return Dl(o)}Yp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Fr(t.pendingLanes);n!==0&&(Fu(t,n|1),We(t,ye()),!(Y&6)&&(hr=ye()+500,dn()))}break;case 13:Ln(function(){var r=Ut(e,1);if(r!==null){var i=De();xt(r,e,1,i)}}),ya(e,1)}};Mu=function(e){if(e.tag===13){var t=Ut(e,134217728);if(t!==null){var n=De();xt(t,e,134217728,n)}ya(e,134217728)}};Zp=function(e){if(e.tag===13){var t=sn(e),n=Ut(e,t);if(n!==null){var r=De();xt(n,e,t,r)}ya(e,t)}};ed=function(){return Z};td=function(e,t){var n=Z;try{return Z=e,t()}finally{Z=n}};bs=function(e,t,n){switch(t){case"input":if(xs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Xl(r);if(!i)throw Error(L(90));Ip(r),xs(r,i)}}}break;case"textarea":Op(e,n);break;case"select":t=n.value,t!=null&&tr(e,!!n.multiple,t,!1)}};Up=pa;Hp=Ln;var Fx={usingClientEntryPoint:!1,Events:[vi,Gn,Xl,Mp,Bp,pa]},Ir={findFiberByHostInstance:wn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Mx={bundleType:Ir.bundleType,version:Ir.version,rendererPackageName:Ir.rendererPackageName,rendererConfig:Ir.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Wp(e),e===null?null:e.stateNode},findFiberByHostInstance:Ir.findFiberByHostInstance||Nx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wi.isDisabled&&Wi.supportsFiber)try{ql=Wi.inject(Mx),_t=Wi}catch{}}nt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Fx;nt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ka(t))throw Error(L(200));return jx(e,t,null,n)};nt.createRoot=function(e,t){if(!ka(e))throw Error(L(299));var n=!1,r="",i=Ch;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ga(e,1,!1,null,null,n,!1,r,i),e[Bt]=t.current,oi(e.nodeType===8?e.parentNode:e),new xa(t)};nt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=Wp(t),e=e===null?null:e.stateNode,e};nt.flushSync=function(e){return Ln(e)};nt.hydrate=function(e,t,n){if(!lo(t))throw Error(L(200));return oo(null,e,t,!0,n)};nt.hydrateRoot=function(e,t,n){if(!ka(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,i=!1,l="",o=Ch;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Sh(t,null,e,1,n??null,i,!1,l,o),e[Bt]=t.current,oi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new io(t)};nt.render=function(e,t,n){if(!lo(t))throw Error(L(200));return oo(null,e,t,!1,n)};nt.unmountComponentAtNode=function(e){if(!lo(e))throw Error(L(40));return e._reactRootContainer?(Ln(function(){oo(null,null,e,!1,function(){e._reactRootContainer=null,e[Bt]=null})}),!0):!1};nt.unstable_batchedUpdates=pa;nt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!lo(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return oo(e,t,n,!1,r)};nt.version="18.3.1-next-f1338f8080-20240426";function Eh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Eh)}catch(e){console.error(e)}}Eh(),Ep.exports=nt;var Bx=Ep.exports,tf=Bx;fs.createRoot=tf.createRoot,fs.hydrateRoot=tf.hydrateRoot;function Ux(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const Hx=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,$x=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Vx={};function nf(e,t){return(Vx.jsx?$x:Hx).test(e)}const Wx=/[ \t\n\f\r]/g;function qx(e){return typeof e=="object"?e.type==="text"?rf(e.value):!1:rf(e)}function rf(e){return e.replace(Wx,"")===""}class Ci{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}Ci.prototype.normal={};Ci.prototype.property={};Ci.prototype.space=void 0;function bh(e,t){const n={},r={};for(const i of e)Object.assign(n,i.property),Object.assign(r,i.normal);return new Ci(n,r,t)}function iu(e){return e.toLowerCase()}class Ge{constructor(t,n){this.attribute=n,this.property=t}}Ge.prototype.attribute="";Ge.prototype.booleanish=!1;Ge.prototype.boolean=!1;Ge.prototype.commaOrSpaceSeparated=!1;Ge.prototype.commaSeparated=!1;Ge.prototype.defined=!1;Ge.prototype.mustUseProperty=!1;Ge.prototype.number=!1;Ge.prototype.overloadedBoolean=!1;Ge.prototype.property="";Ge.prototype.spaceSeparated=!1;Ge.prototype.space=void 0;let Qx=0;const $=Nn(),xe=Nn(),lu=Nn(),I=Nn(),ie=Nn(),sr=Nn(),Xe=Nn();function Nn(){return 2**++Qx}const ou=Object.freeze(Object.defineProperty({__proto__:null,boolean:$,booleanish:xe,commaOrSpaceSeparated:Xe,commaSeparated:sr,number:I,overloadedBoolean:lu,spaceSeparated:ie},Symbol.toStringTag,{value:"Module"})),Qo=Object.keys(ou);class wa extends Ge{constructor(t,n,r,i){let l=-1;if(super(t,n),lf(this,"space",i),typeof r=="number")for(;++l<Qo.length;){const o=Qo[l];lf(this,Qo[l],(r&ou[o])===ou[o])}}}wa.prototype.defined=!0;function lf(e,t,n){n&&(e[t]=n)}function kr(e){const t={},n={};for(const[r,i]of Object.entries(e.properties)){const l=new wa(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[iu(r)]=r,n[iu(l.attribute)]=r}return new Ci(t,n,e.space)}const Th=kr({properties:{ariaActiveDescendant:null,ariaAtomic:xe,ariaAutoComplete:null,ariaBusy:xe,ariaChecked:xe,ariaColCount:I,ariaColIndex:I,ariaColSpan:I,ariaControls:ie,ariaCurrent:null,ariaDescribedBy:ie,ariaDetails:null,ariaDisabled:xe,ariaDropEffect:ie,ariaErrorMessage:null,ariaExpanded:xe,ariaFlowTo:ie,ariaGrabbed:xe,ariaHasPopup:null,ariaHidden:xe,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ie,ariaLevel:I,ariaLive:null,ariaModal:xe,ariaMultiLine:xe,ariaMultiSelectable:xe,ariaOrientation:null,ariaOwns:ie,ariaPlaceholder:null,ariaPosInSet:I,ariaPressed:xe,ariaReadOnly:xe,ariaRelevant:null,ariaRequired:xe,ariaRoleDescription:ie,ariaRowCount:I,ariaRowIndex:I,ariaRowSpan:I,ariaSelected:xe,ariaSetSize:I,ariaSort:null,ariaValueMax:I,ariaValueMin:I,ariaValueNow:I,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function Rh(e,t){return t in e?e[t]:t}function Ph(e,t){return Rh(e,t.toLowerCase())}const Gx=kr({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:sr,acceptCharset:ie,accessKey:ie,action:null,allow:null,allowFullScreen:$,allowPaymentRequest:$,allowUserMedia:$,alt:null,as:null,async:$,autoCapitalize:null,autoComplete:ie,autoFocus:$,autoPlay:$,blocking:ie,capture:null,charSet:null,checked:$,cite:null,className:ie,cols:I,colSpan:null,content:null,contentEditable:xe,controls:$,controlsList:ie,coords:I|sr,crossOrigin:null,data:null,dateTime:null,decoding:null,default:$,defer:$,dir:null,dirName:null,disabled:$,download:lu,draggable:xe,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:$,formTarget:null,headers:ie,height:I,hidden:lu,high:I,href:null,hrefLang:null,htmlFor:ie,httpEquiv:ie,id:null,imageSizes:null,imageSrcSet:null,inert:$,inputMode:null,integrity:null,is:null,isMap:$,itemId:null,itemProp:ie,itemRef:ie,itemScope:$,itemType:ie,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:$,low:I,manifest:null,max:null,maxLength:I,media:null,method:null,min:null,minLength:I,multiple:$,muted:$,name:null,nonce:null,noModule:$,noValidate:$,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:$,optimum:I,pattern:null,ping:ie,placeholder:null,playsInline:$,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:$,referrerPolicy:null,rel:ie,required:$,reversed:$,rows:I,rowSpan:I,sandbox:ie,scope:null,scoped:$,seamless:$,selected:$,shadowRootClonable:$,shadowRootDelegatesFocus:$,shadowRootMode:null,shape:null,size:I,sizes:null,slot:null,span:I,spellCheck:xe,src:null,srcDoc:null,srcLang:null,srcSet:null,start:I,step:null,style:null,tabIndex:I,target:null,title:null,translate:null,type:null,typeMustMatch:$,useMap:null,value:xe,width:I,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ie,axis:null,background:null,bgColor:null,border:I,borderColor:null,bottomMargin:I,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:$,declare:$,event:null,face:null,frame:null,frameBorder:null,hSpace:I,leftMargin:I,link:null,longDesc:null,lowSrc:null,marginHeight:I,marginWidth:I,noResize:$,noHref:$,noShade:$,noWrap:$,object:null,profile:null,prompt:null,rev:null,rightMargin:I,rules:null,scheme:null,scrolling:xe,standby:null,summary:null,text:null,topMargin:I,valueType:null,version:null,vAlign:null,vLink:null,vSpace:I,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:$,disableRemotePlayback:$,prefix:null,property:null,results:I,security:null,unselectable:null},space:"html",transform:Ph}),Kx=kr({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Xe,accentHeight:I,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:I,amplitude:I,arabicForm:null,ascent:I,attributeName:null,attributeType:null,azimuth:I,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:I,by:null,calcMode:null,capHeight:I,className:ie,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:I,diffuseConstant:I,direction:null,display:null,dur:null,divisor:I,dominantBaseline:null,download:$,dx:null,dy:null,edgeMode:null,editable:null,elevation:I,enableBackground:null,end:null,event:null,exponent:I,externalResourcesRequired:null,fill:null,fillOpacity:I,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:sr,g2:sr,glyphName:sr,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:I,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:I,horizOriginX:I,horizOriginY:I,id:null,ideographic:I,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:I,k:I,k1:I,k2:I,k3:I,k4:I,kernelMatrix:Xe,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:I,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:I,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:I,overlineThickness:I,paintOrder:null,panose1:null,path:null,pathLength:I,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ie,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:I,pointsAtY:I,pointsAtZ:I,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Xe,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Xe,rev:Xe,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Xe,requiredFeatures:Xe,requiredFonts:Xe,requiredFormats:Xe,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:I,specularExponent:I,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:I,strikethroughThickness:I,string:null,stroke:null,strokeDashArray:Xe,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:I,strokeOpacity:I,strokeWidth:null,style:null,surfaceScale:I,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Xe,tabIndex:I,tableValues:null,target:null,targetX:I,targetY:I,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Xe,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:I,underlineThickness:I,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:I,values:null,vAlphabetic:I,vMathematical:I,vectorEffect:null,vHanging:I,vIdeographic:I,version:null,vertAdvY:I,vertOriginX:I,vertOriginY:I,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:I,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Rh}),_h=kr({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),Ah=kr({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Ph}),Lh=kr({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),Xx={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},Jx=/[A-Z]/g,of=/-[a-z]/g,Yx=/^data[-\w.:]+$/i;function Zx(e,t){const n=iu(t);let r=t,i=Ge;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Yx.test(t)){if(t.charAt(4)==="-"){const l=t.slice(5).replace(of,t1);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=t.slice(4);if(!of.test(l)){let o=l.replace(Jx,e1);o.charAt(0)!=="-"&&(o="-"+o),t="data"+o}}i=wa}return new i(r,t)}function e1(e){return"-"+e.toLowerCase()}function t1(e){return e.charAt(1).toUpperCase()}const n1=bh([Th,Gx,_h,Ah,Lh],"html"),va=bh([Th,Kx,_h,Ah,Lh],"svg");function r1(e){return e.join(" ").trim()}var Sa={},sf=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,i1=/\n/g,l1=/^\s*/,o1=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,s1=/^:\s*/,u1=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a1=/^[;\s]*/,c1=/^\s+|\s+$/g,f1=`
`,uf="/",af="*",kn="",p1="comment",d1="declaration",h1=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function i(h){var x=h.match(i1);x&&(n+=x.length);var E=h.lastIndexOf(f1);r=~E?h.length-E:r+h.length}function l(){var h={line:n,column:r};return function(x){return x.position=new o(h),a(),x}}function o(h){this.start=h,this.end={line:n,column:r},this.source=t.source}o.prototype.content=e;function s(h){var x=new Error(t.source+":"+n+":"+r+": "+h);if(x.reason=h,x.filename=t.source,x.line=n,x.column=r,x.source=e,!t.silent)throw x}function u(h){var x=h.exec(e);if(x){var E=x[0];return i(E),e=e.slice(E.length),x}}function a(){u(l1)}function c(h){var x;for(h=h||[];x=f();)x!==!1&&h.push(x);return h}function f(){var h=l();if(!(uf!=e.charAt(0)||af!=e.charAt(1))){for(var x=2;kn!=e.charAt(x)&&(af!=e.charAt(x)||uf!=e.charAt(x+1));)++x;if(x+=2,kn===e.charAt(x-1))return s("End of comment missing");var E=e.slice(2,x-2);return r+=2,i(E),e=e.slice(x),r+=2,h({type:p1,comment:E})}}function p(){var h=l(),x=u(o1);if(x){if(f(),!u(s1))return s("property missing ':'");var E=u(u1),m=h({type:d1,property:cf(x[0].replace(sf,kn)),value:E?cf(E[0].replace(sf,kn)):kn});return u(a1),m}}function d(){var h=[];c(h);for(var x;x=p();)x!==!1&&(h.push(x),c(h));return h}return a(),d()};function cf(e){return e?e.replace(c1,kn):kn}var m1=dl&&dl.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Sa,"__esModule",{value:!0});Sa.default=y1;var g1=m1(h1);function y1(e,t){var n=null;if(!e||typeof e!="string")return n;var r=(0,g1.default)(e),i=typeof t=="function";return r.forEach(function(l){if(l.type==="declaration"){var o=l.property,s=l.value;i?t(o,s,l):s&&(n=n||{},n[o]=s)}}),n}var so={};Object.defineProperty(so,"__esModule",{value:!0});so.camelCase=void 0;var x1=/^--[a-zA-Z0-9_-]+$/,k1=/-([a-z])/g,w1=/^[^-]+$/,v1=/^-(webkit|moz|ms|o|khtml)-/,S1=/^-(ms)-/,C1=function(e){return!e||w1.test(e)||x1.test(e)},E1=function(e,t){return t.toUpperCase()},ff=function(e,t){return"".concat(t,"-")},b1=function(e,t){return t===void 0&&(t={}),C1(e)?e:(e=e.toLowerCase(),t.reactCompat?e=e.replace(S1,ff):e=e.replace(v1,ff),e.replace(k1,E1))};so.camelCase=b1;var T1=dl&&dl.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},R1=T1(Sa),P1=so;function su(e,t){var n={};return!e||typeof e!="string"||(0,R1.default)(e,function(r,i){r&&i&&(n[(0,P1.camelCase)(r,t)]=i)}),n}su.default=su;var _1=su;const A1=Eu(_1),Ih=zh("end"),Ca=zh("start");function zh(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function L1(e){const t=Ca(e),n=Ih(e);if(t&&n)return{start:t,end:n}}function Kr(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?pf(e.position):"start"in e||"end"in e?pf(e):"line"in e||"column"in e?uu(e):""}function uu(e){return df(e&&e.line)+":"+df(e&&e.column)}function pf(e){return uu(e&&e.start)+"-"+uu(e&&e.end)}function df(e){return e&&typeof e=="number"?e:1}class Oe extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let i="",l={},o=!1;if(n&&("line"in n&&"column"in n?l={place:n}:"start"in n&&"end"in n?l={place:n}:"type"in n?l={ancestors:[n],place:n.position}:l={...n}),typeof t=="string"?i=t:!l.cause&&t&&(o=!0,i=t.message,l.cause=t),!l.ruleId&&!l.source&&typeof r=="string"){const u=r.indexOf(":");u===-1?l.ruleId=r:(l.source=r.slice(0,u),l.ruleId=r.slice(u+1))}if(!l.place&&l.ancestors&&l.ancestors){const u=l.ancestors[l.ancestors.length-1];u&&(l.place=u.position)}const s=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=s?s.line:void 0,this.name=Kr(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=o&&l.cause&&typeof l.cause.stack=="string"?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}Oe.prototype.file="";Oe.prototype.name="";Oe.prototype.reason="";Oe.prototype.message="";Oe.prototype.stack="";Oe.prototype.column=void 0;Oe.prototype.line=void 0;Oe.prototype.ancestors=void 0;Oe.prototype.cause=void 0;Oe.prototype.fatal=void 0;Oe.prototype.place=void 0;Oe.prototype.ruleId=void 0;Oe.prototype.source=void 0;const Ea={}.hasOwnProperty,I1=new Map,z1=/[A-Z]/g,O1=new Set(["table","tbody","thead","tfoot","tr"]),j1=new Set(["td","th"]),Oh="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function N1(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=V1(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=$1(n,t.jsx,t.jsxs)}const i={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?va:n1,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},l=jh(i,e,void 0);return l&&typeof l!="string"?l:i.create(e,i.Fragment,{children:l||void 0},void 0)}function jh(e,t,n){if(t.type==="element")return D1(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return F1(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return B1(e,t,n);if(t.type==="mdxjsEsm")return M1(e,t);if(t.type==="root")return U1(e,t,n);if(t.type==="text")return H1(e,t)}function D1(e,t,n){const r=e.schema;let i=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=va,e.schema=i),e.ancestors.push(t);const l=Dh(e,t.tagName,!1),o=W1(e,t);let s=Ta(e,t);return O1.has(t.tagName)&&(s=s.filter(function(u){return typeof u=="string"?!qx(u):!0})),Nh(e,o,l,t),ba(o,s),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}function F1(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}mi(e,t.position)}function M1(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);mi(e,t.position)}function B1(e,t,n){const r=e.schema;let i=r;t.name==="svg"&&r.space==="html"&&(i=va,e.schema=i),e.ancestors.push(t);const l=t.name===null?e.Fragment:Dh(e,t.name,!0),o=q1(e,t),s=Ta(e,t);return Nh(e,o,l,t),ba(o,s),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}function U1(e,t,n){const r={};return ba(r,Ta(e,t)),e.create(t,e.Fragment,r,n)}function H1(e,t){return t.value}function Nh(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ba(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function $1(e,t,n){return r;function r(i,l,o,s){const a=Array.isArray(o.children)?n:t;return s?a(l,o,s):a(l,o)}}function V1(e,t){return n;function n(r,i,l,o){const s=Array.isArray(l.children),u=Ca(r);return t(i,l,o,s,{columnNumber:u?u.column-1:void 0,fileName:e,lineNumber:u?u.line:void 0},void 0)}}function W1(e,t){const n={};let r,i;for(i in t.properties)if(i!=="children"&&Ea.call(t.properties,i)){const l=Q1(e,i,t.properties[i]);if(l){const[o,s]=l;e.tableCellAlignToStyle&&o==="align"&&typeof s=="string"&&j1.has(t.tagName)?r=s:n[o]=s}}if(r){const l=n.style||(n.style={});l[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function q1(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const l=r.data.estree.body[0];l.type;const o=l.expression;o.type;const s=o.properties[0];s.type,Object.assign(n,e.evaluater.evaluateExpression(s.argument))}else mi(e,t.position);else{const i=r.name;let l;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const s=r.value.data.estree.body[0];s.type,l=e.evaluater.evaluateExpression(s.expression)}else mi(e,t.position);else l=r.value===null?!0:r.value;n[i]=l}return n}function Ta(e,t){const n=[];let r=-1;const i=e.passKeys?new Map:I1;for(;++r<t.children.length;){const l=t.children[r];let o;if(e.passKeys){const u=l.type==="element"?l.tagName:l.type==="mdxJsxFlowElement"||l.type==="mdxJsxTextElement"?l.name:void 0;if(u){const a=i.get(u)||0;o=u+"-"+a,i.set(u,a+1)}}const s=jh(e,l,o);s!==void 0&&n.push(s)}return n}function Q1(e,t,n){const r=Zx(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?Ux(n):r1(n)),r.property==="style"){let i=typeof n=="object"?n:G1(e,String(n));return e.stylePropertyNameCase==="css"&&(i=K1(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?Xx[r.property]||r.property:r.attribute,n]}}function G1(e,t){try{return A1(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,i=new Oe("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=e.filePath||void 0,i.url=Oh+"#cannot-parse-style-attribute",i}}function Dh(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const i=t.split(".");let l=-1,o;for(;++l<i.length;){const s=nf(i[l])?{type:"Identifier",name:i[l]}:{type:"Literal",value:i[l]};o=o?{type:"MemberExpression",object:o,property:s,computed:!!(l&&s.type==="Literal"),optional:!1}:s}r=o}else r=nf(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const i=r.value;return Ea.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);mi(e)}function mi(e,t){const n=new Oe("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=Oh+"#cannot-handle-mdx-estrees-without-createevaluater",n}function K1(e){const t={};let n;for(n in e)Ea.call(e,n)&&(t[X1(n)]=e[n]);return t}function X1(e){let t=e.replace(z1,J1);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function J1(e){return"-"+e.toLowerCase()}const Go={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},Y1={};function Ra(e,t){const n=Y1,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return Fh(e,r,i)}function Fh(e,t,n){if(Z1(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return hf(e.children,t,n)}return Array.isArray(e)?hf(e,t,n):""}function hf(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=Fh(e[i],t,n);return r.join("")}function Z1(e){return!!(e&&typeof e=="object")}const mf=document.createElement("i");function Pa(e){const t="&"+e+";";mf.innerHTML=t;const n=mf.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function et(e,t,n,r){const i=e.length;let l=0,o;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)o=Array.from(r),o.unshift(t,n),e.splice(...o);else for(n&&e.splice(t,n);l<r.length;)o=r.slice(l,l+1e4),o.unshift(t,0),e.splice(...o),l+=1e4,t+=1e4}function ot(e,t){return e.length>0?(et(e,e.length,0,t),e):t}const gf={}.hasOwnProperty;function Mh(e){const t={};let n=-1;for(;++n<e.length;)e0(t,e[n]);return t}function e0(e,t){let n;for(n in t){const i=(gf.call(e,n)?e[n]:void 0)||(e[n]={}),l=t[n];let o;if(l)for(o in l){gf.call(i,o)||(i[o]=[]);const s=l[o];t0(i[o],Array.isArray(s)?s:s?[s]:[])}}}function t0(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);et(e,0,0,r)}function Bh(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function kt(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Ne=hn(/[A-Za-z]/),Ie=hn(/[\dA-Za-z]/),n0=hn(/[#-'*+\--9=?A-Z^-~]/);function Fl(e){return e!==null&&(e<32||e===127)}const au=hn(/\d/),r0=hn(/[\dA-Fa-f]/),i0=hn(/[!-/:-@[-`{-~]/);function M(e){return e!==null&&e<-2}function re(e){return e!==null&&(e<0||e===32)}function q(e){return e===-2||e===-1||e===32}const uo=hn(new RegExp("\\p{P}|\\p{S}","u")),In=hn(/\s/);function hn(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function wr(e){const t=[];let n=-1,r=0,i=0;for(;++n<e.length;){const l=e.charCodeAt(n);let o="";if(l===37&&Ie(e.charCodeAt(n+1))&&Ie(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){const s=e.charCodeAt(n+1);l<56320&&s>56319&&s<57344?(o=String.fromCharCode(l,s),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function X(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return o;function o(u){return q(u)?(e.enter(n),s(u)):t(u)}function s(u){return q(u)&&l++<i?(e.consume(u),s):(e.exit(n),t(u))}}const l0={tokenize:o0};function o0(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(s){if(s===null){e.consume(s);return}return e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),X(e,t,"linePrefix")}function i(s){return e.enter("paragraph"),l(s)}function l(s){const u=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=u),n=u,o(s)}function o(s){if(s===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(s);return}return M(s)?(e.consume(s),e.exit("chunkText"),l):(e.consume(s),o)}}const s0={tokenize:u0},yf={tokenize:a0};function u0(e){const t=this,n=[];let r=0,i,l,o;return s;function s(y){if(r<n.length){const v=n[r];return t.containerState=v[1],e.attempt(v[0].continuation,u,a)(y)}return a(y)}function u(y){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&g();const v=t.events.length;let T=v,C;for(;T--;)if(t.events[T][0]==="exit"&&t.events[T][1].type==="chunkFlow"){C=t.events[T][1].end;break}m(r);let _=v;for(;_<t.events.length;)t.events[_][1].end={...C},_++;return et(t.events,T+1,0,t.events.slice(v)),t.events.length=_,a(y)}return s(y)}function a(y){if(r===n.length){if(!i)return p(y);if(i.currentConstruct&&i.currentConstruct.concrete)return h(y);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(yf,c,f)(y)}function c(y){return i&&g(),m(r),p(y)}function f(y){return t.parser.lazy[t.now().line]=r!==n.length,o=t.now().offset,h(y)}function p(y){return t.containerState={},e.attempt(yf,d,h)(y)}function d(y){return r++,n.push([t.currentConstruct,t.containerState]),p(y)}function h(y){if(y===null){i&&g(),m(0),e.consume(y);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:l}),x(y)}function x(y){if(y===null){E(e.exit("chunkFlow"),!0),m(0),e.consume(y);return}return M(y)?(e.consume(y),E(e.exit("chunkFlow")),r=0,t.interrupt=void 0,s):(e.consume(y),x)}function E(y,v){const T=t.sliceStream(y);if(v&&T.push(null),y.previous=l,l&&(l.next=y),l=y,i.defineSkip(y.start),i.write(T),t.parser.lazy[y.start.line]){let C=i.events.length;for(;C--;)if(i.events[C][1].start.offset<o&&(!i.events[C][1].end||i.events[C][1].end.offset>o))return;const _=t.events.length;let A=_,N,b;for(;A--;)if(t.events[A][0]==="exit"&&t.events[A][1].type==="chunkFlow"){if(N){b=t.events[A][1].end;break}N=!0}for(m(r),C=_;C<t.events.length;)t.events[C][1].end={...b},C++;et(t.events,A+1,0,t.events.slice(_)),t.events.length=C}}function m(y){let v=n.length;for(;v-- >y;){const T=n[v];t.containerState=T[1],T[0].exit.call(t,e)}n.length=y}function g(){i.write([null]),l=void 0,i=void 0,t.containerState._closeFlow=void 0}}function a0(e,t,n){return X(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function mr(e){if(e===null||re(e)||In(e))return 1;if(uo(e))return 2}function ao(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}const cu={name:"attention",resolveAll:c0,tokenize:f0};function c0(e,t){let n=-1,r,i,l,o,s,u,a,c;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;u=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const f={...e[r][1].end},p={...e[n][1].start};xf(f,-u),xf(p,u),o={type:u>1?"strongSequence":"emphasisSequence",start:f,end:{...e[r][1].end}},s={type:u>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:p},l={type:u>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},i={type:u>1?"strong":"emphasis",start:{...o.start},end:{...s.end}},e[r][1].end={...o.start},e[n][1].start={...s.end},a=[],e[r][1].end.offset-e[r][1].start.offset&&(a=ot(a,[["enter",e[r][1],t],["exit",e[r][1],t]])),a=ot(a,[["enter",i,t],["enter",o,t],["exit",o,t],["enter",l,t]]),a=ot(a,ao(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),a=ot(a,[["exit",l,t],["enter",s,t],["exit",s,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(c=2,a=ot(a,[["enter",e[n][1],t],["exit",e[n][1],t]])):c=0,et(e,r-1,n-r+3,a),n=r+a.length-c-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function f0(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=mr(r);let l;return o;function o(u){return l=u,e.enter("attentionSequence"),s(u)}function s(u){if(u===l)return e.consume(u),s;const a=e.exit("attentionSequence"),c=mr(u),f=!c||c===2&&i||n.includes(u),p=!i||i===2&&c||n.includes(r);return a._open=!!(l===42?f:f&&(i||!p)),a._close=!!(l===42?p:p&&(c||!f)),t(u)}}function xf(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const p0={name:"autolink",tokenize:d0};function d0(e,t,n){let r=0;return i;function i(d){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(d){return Ne(d)?(e.consume(d),o):d===64?n(d):a(d)}function o(d){return d===43||d===45||d===46||Ie(d)?(r=1,s(d)):a(d)}function s(d){return d===58?(e.consume(d),r=0,u):(d===43||d===45||d===46||Ie(d))&&r++<32?(e.consume(d),s):(r=0,a(d))}function u(d){return d===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):d===null||d===32||d===60||Fl(d)?n(d):(e.consume(d),u)}function a(d){return d===64?(e.consume(d),c):n0(d)?(e.consume(d),a):n(d)}function c(d){return Ie(d)?f(d):n(d)}function f(d){return d===46?(e.consume(d),r=0,c):d===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):p(d)}function p(d){if((d===45||Ie(d))&&r++<63){const h=d===45?p:f;return e.consume(d),h}return n(d)}}const Ei={partial:!0,tokenize:h0};function h0(e,t,n){return r;function r(l){return q(l)?X(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||M(l)?t(l):n(l)}}const Uh={continuation:{tokenize:g0},exit:y0,name:"blockQuote",tokenize:m0};function m0(e,t,n){const r=this;return i;function i(o){if(o===62){const s=r.containerState;return s.open||(e.enter("blockQuote",{_container:!0}),s.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(o),e.exit("blockQuoteMarker"),l}return n(o)}function l(o){return q(o)?(e.enter("blockQuotePrefixWhitespace"),e.consume(o),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(o))}}function g0(e,t,n){const r=this;return i;function i(o){return q(o)?X(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o):l(o)}function l(o){return e.attempt(Uh,t,n)(o)}}function y0(e){e.exit("blockQuote")}const Hh={name:"characterEscape",tokenize:x0};function x0(e,t,n){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return i0(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(l)}}const $h={name:"characterReference",tokenize:k0};function k0(e,t,n){const r=this;let i=0,l,o;return s;function s(f){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),u}function u(f){return f===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(f),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),l=31,o=Ie,c(f))}function a(f){return f===88||f===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(f),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,o=r0,c):(e.enter("characterReferenceValue"),l=7,o=au,c(f))}function c(f){if(f===59&&i){const p=e.exit("characterReferenceValue");return o===Ie&&!Pa(r.sliceSerialize(p))?n(f):(e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return o(f)&&i++<l?(e.consume(f),c):n(f)}}const kf={partial:!0,tokenize:v0},wf={concrete:!0,name:"codeFenced",tokenize:w0};function w0(e,t,n){const r=this,i={partial:!0,tokenize:T};let l=0,o=0,s;return u;function u(C){return a(C)}function a(C){const _=r.events[r.events.length-1];return l=_&&_[1].type==="linePrefix"?_[2].sliceSerialize(_[1],!0).length:0,s=C,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),c(C)}function c(C){return C===s?(o++,e.consume(C),c):o<3?n(C):(e.exit("codeFencedFenceSequence"),q(C)?X(e,f,"whitespace")(C):f(C))}function f(C){return C===null||M(C)?(e.exit("codeFencedFence"),r.interrupt?t(C):e.check(kf,x,v)(C)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),p(C))}function p(C){return C===null||M(C)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),f(C)):q(C)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),X(e,d,"whitespace")(C)):C===96&&C===s?n(C):(e.consume(C),p)}function d(C){return C===null||M(C)?f(C):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),h(C))}function h(C){return C===null||M(C)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),f(C)):C===96&&C===s?n(C):(e.consume(C),h)}function x(C){return e.attempt(i,v,E)(C)}function E(C){return e.enter("lineEnding"),e.consume(C),e.exit("lineEnding"),m}function m(C){return l>0&&q(C)?X(e,g,"linePrefix",l+1)(C):g(C)}function g(C){return C===null||M(C)?e.check(kf,x,v)(C):(e.enter("codeFlowValue"),y(C))}function y(C){return C===null||M(C)?(e.exit("codeFlowValue"),g(C)):(e.consume(C),y)}function v(C){return e.exit("codeFenced"),t(C)}function T(C,_,A){let N=0;return b;function b(V){return C.enter("lineEnding"),C.consume(V),C.exit("lineEnding"),j}function j(V){return C.enter("codeFencedFence"),q(V)?X(C,F,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(V):F(V)}function F(V){return V===s?(C.enter("codeFencedFenceSequence"),W(V)):A(V)}function W(V){return V===s?(N++,C.consume(V),W):N>=o?(C.exit("codeFencedFenceSequence"),q(V)?X(C,ee,"whitespace")(V):ee(V)):A(V)}function ee(V){return V===null||M(V)?(C.exit("codeFencedFence"),_(V)):A(V)}}}function v0(e,t,n){const r=this;return i;function i(o){return o===null?n(o):(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l)}function l(o){return r.parser.lazy[r.now().line]?n(o):t(o)}}const Ko={name:"codeIndented",tokenize:C0},S0={partial:!0,tokenize:E0};function C0(e,t,n){const r=this;return i;function i(a){return e.enter("codeIndented"),X(e,l,"linePrefix",5)(a)}function l(a){const c=r.events[r.events.length-1];return c&&c[1].type==="linePrefix"&&c[2].sliceSerialize(c[1],!0).length>=4?o(a):n(a)}function o(a){return a===null?u(a):M(a)?e.attempt(S0,o,u)(a):(e.enter("codeFlowValue"),s(a))}function s(a){return a===null||M(a)?(e.exit("codeFlowValue"),o(a)):(e.consume(a),s)}function u(a){return e.exit("codeIndented"),t(a)}}function E0(e,t,n){const r=this;return i;function i(o){return r.parser.lazy[r.now().line]?n(o):M(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),i):X(e,l,"linePrefix",5)(o)}function l(o){const s=r.events[r.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(o):M(o)?i(o):n(o)}}const b0={name:"codeText",previous:R0,resolve:T0,tokenize:P0};function T0(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function R0(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function P0(e,t,n){let r=0,i,l;return o;function o(f){return e.enter("codeText"),e.enter("codeTextSequence"),s(f)}function s(f){return f===96?(e.consume(f),r++,s):(e.exit("codeTextSequence"),u(f))}function u(f){return f===null?n(f):f===32?(e.enter("space"),e.consume(f),e.exit("space"),u):f===96?(l=e.enter("codeTextSequence"),i=0,c(f)):M(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("codeTextData"),a(f))}function a(f){return f===null||f===32||f===96||M(f)?(e.exit("codeTextData"),u(f)):(e.consume(f),a)}function c(f){return f===96?(e.consume(f),i++,c):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(f)):(l.type="codeTextData",a(f))}}class _0{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const i=n||0;this.setCursor(Math.trunc(t));const l=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&zr(this.left,r),l.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),zr(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),zr(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);zr(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);zr(this.left,n.reverse())}}}function zr(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Vh(e){const t={};let n=-1,r,i,l,o,s,u,a;const c=new _0(e);for(;++n<c.length;){for(;n in t;)n=t[n];if(r=c.get(n),n&&r[1].type==="chunkFlow"&&c.get(n-1)[1].type==="listItemPrefix"&&(u=r[1]._tokenizer.events,l=0,l<u.length&&u[l][1].type==="lineEndingBlank"&&(l+=2),l<u.length&&u[l][1].type==="content"))for(;++l<u.length&&u[l][1].type!=="content";)u[l][1].type==="chunkText"&&(u[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,A0(c,n)),n=t[n],a=!0);else if(r[1]._container){for(l=n,i=void 0;l--;)if(o=c.get(l),o[1].type==="lineEnding"||o[1].type==="lineEndingBlank")o[0]==="enter"&&(i&&(c.get(i)[1].type="lineEndingBlank"),o[1].type="lineEnding",i=l);else if(!(o[1].type==="linePrefix"||o[1].type==="listItemIndent"))break;i&&(r[1].end={...c.get(i)[1].start},s=c.slice(i,n),s.unshift(r),c.splice(i,n-i+1,s))}}return et(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}function A0(e,t){const n=e.get(t)[1],r=e.get(t)[2];let i=t-1;const l=[];let o=n._tokenizer;o||(o=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(o._contentTypeTextTrailing=!0));const s=o.events,u=[],a={};let c,f,p=-1,d=n,h=0,x=0;const E=[x];for(;d;){for(;e.get(++i)[1]!==d;);l.push(i),d._tokenizer||(c=r.sliceStream(d),d.next||c.push(null),f&&o.defineSkip(d.start),d._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=!0),o.write(c),d._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=void 0)),f=d,d=d.next}for(d=n;++p<s.length;)s[p][0]==="exit"&&s[p-1][0]==="enter"&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(x=p+1,E.push(x),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(o.events=[],d?(d._tokenizer=void 0,d.previous=void 0):E.pop(),p=E.length;p--;){const m=s.slice(E[p],E[p+1]),g=l.pop();u.push([g,g+m.length-1]),e.splice(g,2,m)}for(u.reverse(),p=-1;++p<u.length;)a[h+u[p][0]]=h+u[p][1],h+=u[p][1]-u[p][0]-1;return a}const L0={resolve:z0,tokenize:O0},I0={partial:!0,tokenize:j0};function z0(e){return Vh(e),e}function O0(e,t){let n;return r;function r(s){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(s)}function i(s){return s===null?l(s):M(s)?e.check(I0,o,l)(s):(e.consume(s),i)}function l(s){return e.exit("chunkContent"),e.exit("content"),t(s)}function o(s){return e.consume(s),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function j0(e,t,n){const r=this;return i;function i(o){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),X(e,l,"linePrefix")}function l(o){if(o===null||M(o))return n(o);const s=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}function Wh(e,t,n,r,i,l,o,s,u){const a=u||Number.POSITIVE_INFINITY;let c=0;return f;function f(m){return m===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(m),e.exit(l),p):m===null||m===32||m===41||Fl(m)?n(m):(e.enter(r),e.enter(o),e.enter(s),e.enter("chunkString",{contentType:"string"}),x(m))}function p(m){return m===62?(e.enter(l),e.consume(m),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),d(m))}function d(m){return m===62?(e.exit("chunkString"),e.exit(s),p(m)):m===null||m===60||M(m)?n(m):(e.consume(m),m===92?h:d)}function h(m){return m===60||m===62||m===92?(e.consume(m),d):d(m)}function x(m){return!c&&(m===null||m===41||re(m))?(e.exit("chunkString"),e.exit(s),e.exit(o),e.exit(r),t(m)):c<a&&m===40?(e.consume(m),c++,x):m===41?(e.consume(m),c--,x):m===null||m===32||m===40||Fl(m)?n(m):(e.consume(m),m===92?E:x)}function E(m){return m===40||m===41||m===92?(e.consume(m),x):x(m)}}function qh(e,t,n,r,i,l){const o=this;let s=0,u;return a;function a(d){return e.enter(r),e.enter(i),e.consume(d),e.exit(i),e.enter(l),c}function c(d){return s>999||d===null||d===91||d===93&&!u||d===94&&!s&&"_hiddenFootnoteSupport"in o.parser.constructs?n(d):d===93?(e.exit(l),e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):M(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),f(d))}function f(d){return d===null||d===91||d===93||M(d)||s++>999?(e.exit("chunkString"),c(d)):(e.consume(d),u||(u=!q(d)),d===92?p:f)}function p(d){return d===91||d===92||d===93?(e.consume(d),s++,f):f(d)}}function Qh(e,t,n,r,i,l){let o;return s;function s(p){return p===34||p===39||p===40?(e.enter(r),e.enter(i),e.consume(p),e.exit(i),o=p===40?41:p,u):n(p)}function u(p){return p===o?(e.enter(i),e.consume(p),e.exit(i),e.exit(r),t):(e.enter(l),a(p))}function a(p){return p===o?(e.exit(l),u(o)):p===null?n(p):M(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),X(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(p))}function c(p){return p===o||p===null||M(p)?(e.exit("chunkString"),a(p)):(e.consume(p),p===92?f:c)}function f(p){return p===o||p===92?(e.consume(p),c):c(p)}}function Xr(e,t){let n;return r;function r(i){return M(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):q(i)?X(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}const N0={name:"definition",tokenize:F0},D0={partial:!0,tokenize:M0};function F0(e,t,n){const r=this;let i;return l;function l(d){return e.enter("definition"),o(d)}function o(d){return qh.call(r,e,s,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(d)}function s(d){return i=kt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),d===58?(e.enter("definitionMarker"),e.consume(d),e.exit("definitionMarker"),u):n(d)}function u(d){return re(d)?Xr(e,a)(d):a(d)}function a(d){return Wh(e,c,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(d)}function c(d){return e.attempt(D0,f,f)(d)}function f(d){return q(d)?X(e,p,"whitespace")(d):p(d)}function p(d){return d===null||M(d)?(e.exit("definition"),r.parser.defined.push(i),t(d)):n(d)}}function M0(e,t,n){return r;function r(s){return re(s)?Xr(e,i)(s):n(s)}function i(s){return Qh(e,l,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(s)}function l(s){return q(s)?X(e,o,"whitespace")(s):o(s)}function o(s){return s===null||M(s)?t(s):n(s)}}const B0={name:"hardBreakEscape",tokenize:U0};function U0(e,t,n){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return M(l)?(e.exit("hardBreakEscape"),t(l)):n(l)}}const H0={name:"headingAtx",resolve:$0,tokenize:V0};function $0(e,t){let n=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},l={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},et(e,r,n-r+1,[["enter",i,t],["enter",l,t],["exit",l,t],["exit",i,t]])),e}function V0(e,t,n){let r=0;return i;function i(c){return e.enter("atxHeading"),l(c)}function l(c){return e.enter("atxHeadingSequence"),o(c)}function o(c){return c===35&&r++<6?(e.consume(c),o):c===null||re(c)?(e.exit("atxHeadingSequence"),s(c)):n(c)}function s(c){return c===35?(e.enter("atxHeadingSequence"),u(c)):c===null||M(c)?(e.exit("atxHeading"),t(c)):q(c)?X(e,s,"whitespace")(c):(e.enter("atxHeadingText"),a(c))}function u(c){return c===35?(e.consume(c),u):(e.exit("atxHeadingSequence"),s(c))}function a(c){return c===null||c===35||re(c)?(e.exit("atxHeadingText"),s(c)):(e.consume(c),a)}}const W0=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],vf=["pre","script","style","textarea"],q0={concrete:!0,name:"htmlFlow",resolveTo:K0,tokenize:X0},Q0={partial:!0,tokenize:Y0},G0={partial:!0,tokenize:J0};function K0(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function X0(e,t,n){const r=this;let i,l,o,s,u;return a;function a(S){return c(S)}function c(S){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(S),f}function f(S){return S===33?(e.consume(S),p):S===47?(e.consume(S),l=!0,x):S===63?(e.consume(S),i=3,r.interrupt?t:k):Ne(S)?(e.consume(S),o=String.fromCharCode(S),E):n(S)}function p(S){return S===45?(e.consume(S),i=2,d):S===91?(e.consume(S),i=5,s=0,h):Ne(S)?(e.consume(S),i=4,r.interrupt?t:k):n(S)}function d(S){return S===45?(e.consume(S),r.interrupt?t:k):n(S)}function h(S){const we="CDATA[";return S===we.charCodeAt(s++)?(e.consume(S),s===we.length?r.interrupt?t:F:h):n(S)}function x(S){return Ne(S)?(e.consume(S),o=String.fromCharCode(S),E):n(S)}function E(S){if(S===null||S===47||S===62||re(S)){const we=S===47,ft=o.toLowerCase();return!we&&!l&&vf.includes(ft)?(i=1,r.interrupt?t(S):F(S)):W0.includes(o.toLowerCase())?(i=6,we?(e.consume(S),m):r.interrupt?t(S):F(S)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(S):l?g(S):y(S))}return S===45||Ie(S)?(e.consume(S),o+=String.fromCharCode(S),E):n(S)}function m(S){return S===62?(e.consume(S),r.interrupt?t:F):n(S)}function g(S){return q(S)?(e.consume(S),g):b(S)}function y(S){return S===47?(e.consume(S),b):S===58||S===95||Ne(S)?(e.consume(S),v):q(S)?(e.consume(S),y):b(S)}function v(S){return S===45||S===46||S===58||S===95||Ie(S)?(e.consume(S),v):T(S)}function T(S){return S===61?(e.consume(S),C):q(S)?(e.consume(S),T):y(S)}function C(S){return S===null||S===60||S===61||S===62||S===96?n(S):S===34||S===39?(e.consume(S),u=S,_):q(S)?(e.consume(S),C):A(S)}function _(S){return S===u?(e.consume(S),u=null,N):S===null||M(S)?n(S):(e.consume(S),_)}function A(S){return S===null||S===34||S===39||S===47||S===60||S===61||S===62||S===96||re(S)?T(S):(e.consume(S),A)}function N(S){return S===47||S===62||q(S)?y(S):n(S)}function b(S){return S===62?(e.consume(S),j):n(S)}function j(S){return S===null||M(S)?F(S):q(S)?(e.consume(S),j):n(S)}function F(S){return S===45&&i===2?(e.consume(S),ae):S===60&&i===1?(e.consume(S),ge):S===62&&i===4?(e.consume(S),J):S===63&&i===3?(e.consume(S),k):S===93&&i===5?(e.consume(S),B):M(S)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(Q0,te,W)(S)):S===null||M(S)?(e.exit("htmlFlowData"),W(S)):(e.consume(S),F)}function W(S){return e.check(G0,ee,te)(S)}function ee(S){return e.enter("lineEnding"),e.consume(S),e.exit("lineEnding"),V}function V(S){return S===null||M(S)?W(S):(e.enter("htmlFlowData"),F(S))}function ae(S){return S===45?(e.consume(S),k):F(S)}function ge(S){return S===47?(e.consume(S),o="",O):F(S)}function O(S){if(S===62){const we=o.toLowerCase();return vf.includes(we)?(e.consume(S),J):F(S)}return Ne(S)&&o.length<8?(e.consume(S),o+=String.fromCharCode(S),O):F(S)}function B(S){return S===93?(e.consume(S),k):F(S)}function k(S){return S===62?(e.consume(S),J):S===45&&i===2?(e.consume(S),k):F(S)}function J(S){return S===null||M(S)?(e.exit("htmlFlowData"),te(S)):(e.consume(S),J)}function te(S){return e.exit("htmlFlow"),t(S)}}function J0(e,t,n){const r=this;return i;function i(o){return M(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l):n(o)}function l(o){return r.parser.lazy[r.now().line]?n(o):t(o)}}function Y0(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(Ei,t,n)}}const Z0={name:"htmlText",tokenize:ek};function ek(e,t,n){const r=this;let i,l,o;return s;function s(k){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(k),u}function u(k){return k===33?(e.consume(k),a):k===47?(e.consume(k),T):k===63?(e.consume(k),y):Ne(k)?(e.consume(k),A):n(k)}function a(k){return k===45?(e.consume(k),c):k===91?(e.consume(k),l=0,h):Ne(k)?(e.consume(k),g):n(k)}function c(k){return k===45?(e.consume(k),d):n(k)}function f(k){return k===null?n(k):k===45?(e.consume(k),p):M(k)?(o=f,ge(k)):(e.consume(k),f)}function p(k){return k===45?(e.consume(k),d):f(k)}function d(k){return k===62?ae(k):k===45?p(k):f(k)}function h(k){const J="CDATA[";return k===J.charCodeAt(l++)?(e.consume(k),l===J.length?x:h):n(k)}function x(k){return k===null?n(k):k===93?(e.consume(k),E):M(k)?(o=x,ge(k)):(e.consume(k),x)}function E(k){return k===93?(e.consume(k),m):x(k)}function m(k){return k===62?ae(k):k===93?(e.consume(k),m):x(k)}function g(k){return k===null||k===62?ae(k):M(k)?(o=g,ge(k)):(e.consume(k),g)}function y(k){return k===null?n(k):k===63?(e.consume(k),v):M(k)?(o=y,ge(k)):(e.consume(k),y)}function v(k){return k===62?ae(k):y(k)}function T(k){return Ne(k)?(e.consume(k),C):n(k)}function C(k){return k===45||Ie(k)?(e.consume(k),C):_(k)}function _(k){return M(k)?(o=_,ge(k)):q(k)?(e.consume(k),_):ae(k)}function A(k){return k===45||Ie(k)?(e.consume(k),A):k===47||k===62||re(k)?N(k):n(k)}function N(k){return k===47?(e.consume(k),ae):k===58||k===95||Ne(k)?(e.consume(k),b):M(k)?(o=N,ge(k)):q(k)?(e.consume(k),N):ae(k)}function b(k){return k===45||k===46||k===58||k===95||Ie(k)?(e.consume(k),b):j(k)}function j(k){return k===61?(e.consume(k),F):M(k)?(o=j,ge(k)):q(k)?(e.consume(k),j):N(k)}function F(k){return k===null||k===60||k===61||k===62||k===96?n(k):k===34||k===39?(e.consume(k),i=k,W):M(k)?(o=F,ge(k)):q(k)?(e.consume(k),F):(e.consume(k),ee)}function W(k){return k===i?(e.consume(k),i=void 0,V):k===null?n(k):M(k)?(o=W,ge(k)):(e.consume(k),W)}function ee(k){return k===null||k===34||k===39||k===60||k===61||k===96?n(k):k===47||k===62||re(k)?N(k):(e.consume(k),ee)}function V(k){return k===47||k===62||re(k)?N(k):n(k)}function ae(k){return k===62?(e.consume(k),e.exit("htmlTextData"),e.exit("htmlText"),t):n(k)}function ge(k){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(k),e.exit("lineEnding"),O}function O(k){return q(k)?X(e,B,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(k):B(k)}function B(k){return e.enter("htmlTextData"),o(k)}}const _a={name:"labelEnd",resolveAll:ik,resolveTo:lk,tokenize:ok},tk={tokenize:sk},nk={tokenize:uk},rk={tokenize:ak};function ik(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",t+=i}}return e.length!==n.length&&et(e,0,e.length,n),e}function lk(e,t){let n=e.length,r=0,i,l,o,s;for(;n--;)if(i=e[n][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(o){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(o=n);const u={type:e[l][1].type==="labelLink"?"link":"image",start:{...e[l][1].start},end:{...e[e.length-1][1].end}},a={type:"label",start:{...e[l][1].start},end:{...e[o][1].end}},c={type:"labelText",start:{...e[l+r+2][1].end},end:{...e[o-2][1].start}};return s=[["enter",u,t],["enter",a,t]],s=ot(s,e.slice(l+1,l+r+3)),s=ot(s,[["enter",c,t]]),s=ot(s,ao(t.parser.constructs.insideSpan.null,e.slice(l+r+4,o-3),t)),s=ot(s,[["exit",c,t],e[o-2],e[o-1],["exit",a,t]]),s=ot(s,e.slice(o+1)),s=ot(s,[["exit",u,t]]),et(e,l,e.length,s),e}function ok(e,t,n){const r=this;let i=r.events.length,l,o;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return s;function s(p){return l?l._inactive?f(p):(o=r.parser.defined.includes(kt(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(p),e.exit("labelMarker"),e.exit("labelEnd"),u):n(p)}function u(p){return p===40?e.attempt(tk,c,o?c:f)(p):p===91?e.attempt(nk,c,o?a:f)(p):o?c(p):f(p)}function a(p){return e.attempt(rk,c,f)(p)}function c(p){return t(p)}function f(p){return l._balanced=!0,n(p)}}function sk(e,t,n){return r;function r(f){return e.enter("resource"),e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),i}function i(f){return re(f)?Xr(e,l)(f):l(f)}function l(f){return f===41?c(f):Wh(e,o,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function o(f){return re(f)?Xr(e,u)(f):c(f)}function s(f){return n(f)}function u(f){return f===34||f===39||f===40?Qh(e,a,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):c(f)}function a(f){return re(f)?Xr(e,c)(f):c(f)}function c(f){return f===41?(e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),e.exit("resource"),t):n(f)}}function uk(e,t,n){const r=this;return i;function i(s){return qh.call(r,e,l,o,"reference","referenceMarker","referenceString")(s)}function l(s){return r.parser.defined.includes(kt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(s):n(s)}function o(s){return n(s)}}function ak(e,t,n){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),t):n(l)}}const ck={name:"labelStartImage",resolveAll:_a.resolveAll,tokenize:fk};function fk(e,t,n){const r=this;return i;function i(s){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(s),e.exit("labelImageMarker"),l}function l(s){return s===91?(e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelImage"),o):n(s)}function o(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const pk={name:"labelStartLink",resolveAll:_a.resolveAll,tokenize:dk};function dk(e,t,n){const r=this;return i;function i(o){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelLink"),l}function l(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(o):t(o)}}const Xo={name:"lineEnding",tokenize:hk};function hk(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),X(e,t,"linePrefix")}}const ul={name:"thematicBreak",tokenize:mk};function mk(e,t,n){let r=0,i;return l;function l(a){return e.enter("thematicBreak"),o(a)}function o(a){return i=a,s(a)}function s(a){return a===i?(e.enter("thematicBreakSequence"),u(a)):r>=3&&(a===null||M(a))?(e.exit("thematicBreak"),t(a)):n(a)}function u(a){return a===i?(e.consume(a),r++,u):(e.exit("thematicBreakSequence"),q(a)?X(e,s,"whitespace")(a):s(a))}}const Be={continuation:{tokenize:kk},exit:vk,name:"list",tokenize:xk},gk={partial:!0,tokenize:Sk},yk={partial:!0,tokenize:wk};function xk(e,t,n){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,o=0;return s;function s(d){const h=r.containerState.type||(d===42||d===43||d===45?"listUnordered":"listOrdered");if(h==="listUnordered"?!r.containerState.marker||d===r.containerState.marker:au(d)){if(r.containerState.type||(r.containerState.type=h,e.enter(h,{_container:!0})),h==="listUnordered")return e.enter("listItemPrefix"),d===42||d===45?e.check(ul,n,a)(d):a(d);if(!r.interrupt||d===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),u(d)}return n(d)}function u(d){return au(d)&&++o<10?(e.consume(d),u):(!r.interrupt||o<2)&&(r.containerState.marker?d===r.containerState.marker:d===41||d===46)?(e.exit("listItemValue"),a(d)):n(d)}function a(d){return e.enter("listItemMarker"),e.consume(d),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||d,e.check(Ei,r.interrupt?n:c,e.attempt(gk,p,f))}function c(d){return r.containerState.initialBlankLine=!0,l++,p(d)}function f(d){return q(d)?(e.enter("listItemPrefixWhitespace"),e.consume(d),e.exit("listItemPrefixWhitespace"),p):n(d)}function p(d){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(d)}}function kk(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Ei,i,l);function i(s){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,X(e,t,"listItemIndent",r.containerState.size+1)(s)}function l(s){return r.containerState.furtherBlankLines||!q(s)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(s)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(yk,t,o)(s))}function o(s){return r.containerState._closeFlow=!0,r.interrupt=void 0,X(e,e.attempt(Be,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s)}}function wk(e,t,n){const r=this;return X(e,i,"listItemIndent",r.containerState.size+1);function i(l){const o=r.events[r.events.length-1];return o&&o[1].type==="listItemIndent"&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(l):n(l)}}function vk(e){e.exit(this.containerState.type)}function Sk(e,t,n){const r=this;return X(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(l){const o=r.events[r.events.length-1];return!q(l)&&o&&o[1].type==="listItemPrefixWhitespace"?t(l):n(l)}}const Sf={name:"setextUnderline",resolveTo:Ck,tokenize:Ek};function Ck(e,t){let n=e.length,r,i,l;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!l&&e[n][1].type==="definition"&&(l=n);const o={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",o,t]),e.splice(l+1,0,["exit",e[r][1],t]),e[r][1].end={...e[l][1].end}):e[r][1]=o,e.push(["exit",o,t]),e}function Ek(e,t,n){const r=this;let i;return l;function l(a){let c=r.events.length,f;for(;c--;)if(r.events[c][1].type!=="lineEnding"&&r.events[c][1].type!=="linePrefix"&&r.events[c][1].type!=="content"){f=r.events[c][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||f)?(e.enter("setextHeadingLine"),i=a,o(a)):n(a)}function o(a){return e.enter("setextHeadingLineSequence"),s(a)}function s(a){return a===i?(e.consume(a),s):(e.exit("setextHeadingLineSequence"),q(a)?X(e,u,"lineSuffix")(a):u(a))}function u(a){return a===null||M(a)?(e.exit("setextHeadingLine"),t(a)):n(a)}}const bk={tokenize:Tk};function Tk(e){const t=this,n=e.attempt(Ei,r,e.attempt(this.parser.constructs.flowInitial,i,X(e,e.attempt(this.parser.constructs.flow,i,e.attempt(L0,i)),"linePrefix")));return n;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const Rk={resolveAll:Kh()},Pk=Gh("string"),_k=Gh("text");function Gh(e){return{resolveAll:Kh(e==="text"?Ak:void 0),tokenize:t};function t(n){const r=this,i=this.parser.constructs[e],l=n.attempt(i,o,s);return o;function o(c){return a(c)?l(c):s(c)}function s(c){if(c===null){n.consume(c);return}return n.enter("data"),n.consume(c),u}function u(c){return a(c)?(n.exit("data"),l(c)):(n.consume(c),u)}function a(c){if(c===null)return!0;const f=i[c];let p=-1;if(f)for(;++p<f.length;){const d=f[p];if(!d.previous||d.previous.call(r,r.previous))return!0}return!1}}}function Kh(e){return t;function t(n,r){let i=-1,l;for(;++i<=n.length;)l===void 0?n[i]&&n[i][1].type==="data"&&(l=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==l+2&&(n[l][1].end=n[i-1][1].end,n.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(n,r):n}}function Ak(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let l=i.length,o=-1,s=0,u;for(;l--;){const a=i[l];if(typeof a=="string"){for(o=a.length;a.charCodeAt(o-1)===32;)s++,o--;if(o)break;o=-1}else if(a===-2)u=!0,s++;else if(a!==-1){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){const a={type:n===e.length||u||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?o:r.start._bufferIndex+o,_index:r.start._index+l,line:r.end.line,column:r.end.column-s,offset:r.end.offset-s},end:{...r.end}};r.end={...a.start},r.start.offset===r.end.offset?Object.assign(r,a):(e.splice(n,0,["enter",a,t],["exit",a,t]),n+=2)}n++}return e}const Lk={42:Be,43:Be,45:Be,48:Be,49:Be,50:Be,51:Be,52:Be,53:Be,54:Be,55:Be,56:Be,57:Be,62:Uh},Ik={91:N0},zk={[-2]:Ko,[-1]:Ko,32:Ko},Ok={35:H0,42:ul,45:[Sf,ul],60:q0,61:Sf,95:ul,96:wf,126:wf},jk={38:$h,92:Hh},Nk={[-5]:Xo,[-4]:Xo,[-3]:Xo,33:ck,38:$h,42:cu,60:[p0,Z0],91:pk,92:[B0,Hh],93:_a,95:cu,96:b0},Dk={null:[cu,Rk]},Fk={null:[42,95]},Mk={null:[]},Bk=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:Fk,contentInitial:Ik,disable:Mk,document:Lk,flow:Ok,flowInitial:zk,insideSpan:Dk,string:jk,text:Nk},Symbol.toStringTag,{value:"Module"}));function Uk(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const i={},l=[];let o=[],s=[];const u={attempt:_(T),check:_(C),consume:g,enter:y,exit:v,interrupt:_(C,{interrupt:!0})},a={code:null,containerState:{},defineSkip:x,events:[],now:h,parser:e,previous:null,sliceSerialize:p,sliceStream:d,write:f};let c=t.tokenize.call(a,u);return t.resolveAll&&l.push(t),a;function f(j){return o=ot(o,j),E(),o[o.length-1]!==null?[]:(A(t,0),a.events=ao(l,a.events,a),a.events)}function p(j,F){return $k(d(j),F)}function d(j){return Hk(o,j)}function h(){const{_bufferIndex:j,_index:F,line:W,column:ee,offset:V}=r;return{_bufferIndex:j,_index:F,line:W,column:ee,offset:V}}function x(j){i[j.line]=j.column,b()}function E(){let j;for(;r._index<o.length;){const F=o[r._index];if(typeof F=="string")for(j=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===j&&r._bufferIndex<F.length;)m(F.charCodeAt(r._bufferIndex));else m(F)}}function m(j){c=c(j)}function g(j){M(j)?(r.line++,r.column=1,r.offset+=j===-3?2:1,b()):j!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),a.previous=j}function y(j,F){const W=F||{};return W.type=j,W.start=h(),a.events.push(["enter",W,a]),s.push(W),W}function v(j){const F=s.pop();return F.end=h(),a.events.push(["exit",F,a]),F}function T(j,F){A(j,F.from)}function C(j,F){F.restore()}function _(j,F){return W;function W(ee,V,ae){let ge,O,B,k;return Array.isArray(ee)?te(ee):"tokenize"in ee?te([ee]):J(ee);function J(oe){return St;function St(Wt){const Dn=Wt!==null&&oe[Wt],Fn=Wt!==null&&oe.null,Pi=[...Array.isArray(Dn)?Dn:Dn?[Dn]:[],...Array.isArray(Fn)?Fn:Fn?[Fn]:[]];return te(Pi)(Wt)}}function te(oe){return ge=oe,O=0,oe.length===0?ae:S(oe[O])}function S(oe){return St;function St(Wt){return k=N(),B=oe,oe.partial||(a.currentConstruct=oe),oe.name&&a.parser.constructs.disable.null.includes(oe.name)?ft():oe.tokenize.call(F?Object.assign(Object.create(a),F):a,u,we,ft)(Wt)}}function we(oe){return j(B,k),V}function ft(oe){return k.restore(),++O<ge.length?S(ge[O]):ae}}}function A(j,F){j.resolveAll&&!l.includes(j)&&l.push(j),j.resolve&&et(a.events,F,a.events.length-F,j.resolve(a.events.slice(F),a)),j.resolveTo&&(a.events=j.resolveTo(a.events,a))}function N(){const j=h(),F=a.previous,W=a.currentConstruct,ee=a.events.length,V=Array.from(s);return{from:ee,restore:ae};function ae(){r=j,a.previous=F,a.currentConstruct=W,a.events.length=ee,s=V,b()}}function b(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function Hk(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,l=t.end._bufferIndex;let o;if(n===i)o=[e[n].slice(r,l)];else{if(o=e.slice(n,i),r>-1){const s=o[0];typeof s=="string"?o[0]=s.slice(r):o.shift()}l>0&&o.push(e[i].slice(0,l))}return o}function $k(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const l=e[n];let o;if(typeof l=="string")o=l;else switch(l){case-5:{o="\r";break}case-4:{o=`
`;break}case-3:{o=`\r
`;break}case-2:{o=t?" ":"	";break}case-1:{if(!t&&i)continue;o=" ";break}default:o=String.fromCharCode(l)}i=l===-2,r.push(o)}return r.join("")}function Vk(e){const r={constructs:Mh([Bk,...(e||{}).extensions||[]]),content:i(l0),defined:[],document:i(s0),flow:i(bk),lazy:{},string:i(Pk),text:i(_k)};return r;function i(l){return o;function o(s){return Uk(r,l,s)}}}function Wk(e){for(;!Vh(e););return e}const Cf=/[\0\t\n\r]/g;function qk(){let e=1,t="",n=!0,r;return i;function i(l,o,s){const u=[];let a,c,f,p,d;for(l=t+(typeof l=="string"?l.toString():new TextDecoder(o||void 0).decode(l)),f=0,t="",n&&(l.charCodeAt(0)===65279&&f++,n=void 0);f<l.length;){if(Cf.lastIndex=f,a=Cf.exec(l),p=a&&a.index!==void 0?a.index:l.length,d=l.charCodeAt(p),!a){t=l.slice(f);break}if(d===10&&f===p&&r)u.push(-3),r=void 0;else switch(r&&(u.push(-5),r=void 0),f<p&&(u.push(l.slice(f,p)),e+=p-f),d){case 0:{u.push(65533),e++;break}case 9:{for(c=Math.ceil(e/4)*4,u.push(-2);e++<c;)u.push(-1);break}case 10:{u.push(-4),e=1;break}default:r=!0,e=1}f=p+1}return s&&(r&&u.push(-5),t&&u.push(t),u.push(null)),u}}const Qk=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Gk(e){return e.replace(Qk,Kk)}function Kk(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),l=i===120||i===88;return Bh(n.slice(l?2:1),l?16:10)}return Pa(n)||e}const Xh={}.hasOwnProperty;function Xk(e,t,n){return typeof t!="string"&&(n=t,t=void 0),Jk(n)(Wk(Vk(n).document().write(qk()(e,t,!0))))}function Jk(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:l(Wa),autolinkProtocol:N,autolinkEmail:N,atxHeading:l(Ha),blockQuote:l(Fn),characterEscape:N,characterReference:N,codeFenced:l(Pi),codeFencedFenceInfo:o,codeFencedFenceMeta:o,codeIndented:l(Pi,o),codeText:l(og,o),codeTextData:N,data:N,codeFlowValue:N,definition:l(sg),definitionDestinationString:o,definitionLabelString:o,definitionTitleString:o,emphasis:l(ug),hardBreakEscape:l($a),hardBreakTrailing:l($a),htmlFlow:l(Va,o),htmlFlowData:N,htmlText:l(Va,o),htmlTextData:N,image:l(ag),label:o,link:l(Wa),listItem:l(cg),listItemValue:p,listOrdered:l(qa,f),listUnordered:l(qa),paragraph:l(fg),reference:S,referenceString:o,resourceDestinationString:o,resourceTitleString:o,setextHeading:l(Ha),strong:l(pg),thematicBreak:l(hg)},exit:{atxHeading:u(),atxHeadingSequence:T,autolink:u(),autolinkEmail:Dn,autolinkProtocol:Wt,blockQuote:u(),characterEscapeValue:b,characterReferenceMarkerHexadecimal:ft,characterReferenceMarkerNumeric:ft,characterReferenceValue:oe,characterReference:St,codeFenced:u(E),codeFencedFence:x,codeFencedFenceInfo:d,codeFencedFenceMeta:h,codeFlowValue:b,codeIndented:u(m),codeText:u(V),codeTextData:b,data:b,definition:u(),definitionDestinationString:v,definitionLabelString:g,definitionTitleString:y,emphasis:u(),hardBreakEscape:u(F),hardBreakTrailing:u(F),htmlFlow:u(W),htmlFlowData:b,htmlText:u(ee),htmlTextData:b,image:u(ge),label:B,labelText:O,lineEnding:j,link:u(ae),listItem:u(),listOrdered:u(),listUnordered:u(),paragraph:u(),referenceString:we,resourceDestinationString:k,resourceTitleString:J,resource:te,setextHeading:u(A),setextHeadingLineSequence:_,setextHeadingText:C,strong:u(),thematicBreak:u()}};Jh(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(P){let z={type:"root",children:[]};const U={stack:[z],tokenStack:[],config:t,enter:s,exit:a,buffer:o,resume:c,data:n},G=[];let ne=-1;for(;++ne<P.length;)if(P[ne][1].type==="listOrdered"||P[ne][1].type==="listUnordered")if(P[ne][0]==="enter")G.push(ne);else{const pt=G.pop();ne=i(P,pt,ne)}for(ne=-1;++ne<P.length;){const pt=t[P[ne][0]];Xh.call(pt,P[ne][1].type)&&pt[P[ne][1].type].call(Object.assign({sliceSerialize:P[ne][2].sliceSerialize},U),P[ne][1])}if(U.tokenStack.length>0){const pt=U.tokenStack[U.tokenStack.length-1];(pt[1]||Ef).call(U,void 0,pt[0])}for(z.position={start:Qt(P.length>0?P[0][1].start:{line:1,column:1,offset:0}),end:Qt(P.length>0?P[P.length-2][1].end:{line:1,column:1,offset:0})},ne=-1;++ne<t.transforms.length;)z=t.transforms[ne](z)||z;return z}function i(P,z,U){let G=z-1,ne=-1,pt=!1,mn,It,Cr,Er;for(;++G<=U;){const Ke=P[G];switch(Ke[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{Ke[0]==="enter"?ne++:ne--,Er=void 0;break}case"lineEndingBlank":{Ke[0]==="enter"&&(mn&&!Er&&!ne&&!Cr&&(Cr=G),Er=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Er=void 0}if(!ne&&Ke[0]==="enter"&&Ke[1].type==="listItemPrefix"||ne===-1&&Ke[0]==="exit"&&(Ke[1].type==="listUnordered"||Ke[1].type==="listOrdered")){if(mn){let Mn=G;for(It=void 0;Mn--;){const zt=P[Mn];if(zt[1].type==="lineEnding"||zt[1].type==="lineEndingBlank"){if(zt[0]==="exit")continue;It&&(P[It][1].type="lineEndingBlank",pt=!0),zt[1].type="lineEnding",It=Mn}else if(!(zt[1].type==="linePrefix"||zt[1].type==="blockQuotePrefix"||zt[1].type==="blockQuotePrefixWhitespace"||zt[1].type==="blockQuoteMarker"||zt[1].type==="listItemIndent"))break}Cr&&(!It||Cr<It)&&(mn._spread=!0),mn.end=Object.assign({},It?P[It][1].start:Ke[1].end),P.splice(It||G,0,["exit",mn,Ke[2]]),G++,U++}if(Ke[1].type==="listItemPrefix"){const Mn={type:"listItem",_spread:!1,start:Object.assign({},Ke[1].start),end:void 0};mn=Mn,P.splice(G,0,["enter",Mn,Ke[2]]),G++,U++,Cr=void 0,Er=!0}}}return P[z][1]._spread=pt,U}function l(P,z){return U;function U(G){s.call(this,P(G),G),z&&z.call(this,G)}}function o(){this.stack.push({type:"fragment",children:[]})}function s(P,z,U){this.stack[this.stack.length-1].children.push(P),this.stack.push(P),this.tokenStack.push([z,U||void 0]),P.position={start:Qt(z.start),end:void 0}}function u(P){return z;function z(U){P&&P.call(this,U),a.call(this,U)}}function a(P,z){const U=this.stack.pop(),G=this.tokenStack.pop();if(G)G[0].type!==P.type&&(z?z.call(this,P,G[0]):(G[1]||Ef).call(this,P,G[0]));else throw new Error("Cannot close `"+P.type+"` ("+Kr({start:P.start,end:P.end})+"): it’s not open");U.position.end=Qt(P.end)}function c(){return Ra(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(P){if(this.data.expectingFirstListItemValue){const z=this.stack[this.stack.length-2];z.start=Number.parseInt(this.sliceSerialize(P),10),this.data.expectingFirstListItemValue=void 0}}function d(){const P=this.resume(),z=this.stack[this.stack.length-1];z.lang=P}function h(){const P=this.resume(),z=this.stack[this.stack.length-1];z.meta=P}function x(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function E(){const P=this.resume(),z=this.stack[this.stack.length-1];z.value=P.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function m(){const P=this.resume(),z=this.stack[this.stack.length-1];z.value=P.replace(/(\r?\n|\r)$/g,"")}function g(P){const z=this.resume(),U=this.stack[this.stack.length-1];U.label=z,U.identifier=kt(this.sliceSerialize(P)).toLowerCase()}function y(){const P=this.resume(),z=this.stack[this.stack.length-1];z.title=P}function v(){const P=this.resume(),z=this.stack[this.stack.length-1];z.url=P}function T(P){const z=this.stack[this.stack.length-1];if(!z.depth){const U=this.sliceSerialize(P).length;z.depth=U}}function C(){this.data.setextHeadingSlurpLineEnding=!0}function _(P){const z=this.stack[this.stack.length-1];z.depth=this.sliceSerialize(P).codePointAt(0)===61?1:2}function A(){this.data.setextHeadingSlurpLineEnding=void 0}function N(P){const U=this.stack[this.stack.length-1].children;let G=U[U.length-1];(!G||G.type!=="text")&&(G=dg(),G.position={start:Qt(P.start),end:void 0},U.push(G)),this.stack.push(G)}function b(P){const z=this.stack.pop();z.value+=this.sliceSerialize(P),z.position.end=Qt(P.end)}function j(P){const z=this.stack[this.stack.length-1];if(this.data.atHardBreak){const U=z.children[z.children.length-1];U.position.end=Qt(P.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(z.type)&&(N.call(this,P),b.call(this,P))}function F(){this.data.atHardBreak=!0}function W(){const P=this.resume(),z=this.stack[this.stack.length-1];z.value=P}function ee(){const P=this.resume(),z=this.stack[this.stack.length-1];z.value=P}function V(){const P=this.resume(),z=this.stack[this.stack.length-1];z.value=P}function ae(){const P=this.stack[this.stack.length-1];if(this.data.inReference){const z=this.data.referenceType||"shortcut";P.type+="Reference",P.referenceType=z,delete P.url,delete P.title}else delete P.identifier,delete P.label;this.data.referenceType=void 0}function ge(){const P=this.stack[this.stack.length-1];if(this.data.inReference){const z=this.data.referenceType||"shortcut";P.type+="Reference",P.referenceType=z,delete P.url,delete P.title}else delete P.identifier,delete P.label;this.data.referenceType=void 0}function O(P){const z=this.sliceSerialize(P),U=this.stack[this.stack.length-2];U.label=Gk(z),U.identifier=kt(z).toLowerCase()}function B(){const P=this.stack[this.stack.length-1],z=this.resume(),U=this.stack[this.stack.length-1];if(this.data.inReference=!0,U.type==="link"){const G=P.children;U.children=G}else U.alt=z}function k(){const P=this.resume(),z=this.stack[this.stack.length-1];z.url=P}function J(){const P=this.resume(),z=this.stack[this.stack.length-1];z.title=P}function te(){this.data.inReference=void 0}function S(){this.data.referenceType="collapsed"}function we(P){const z=this.resume(),U=this.stack[this.stack.length-1];U.label=z,U.identifier=kt(this.sliceSerialize(P)).toLowerCase(),this.data.referenceType="full"}function ft(P){this.data.characterReferenceType=P.type}function oe(P){const z=this.sliceSerialize(P),U=this.data.characterReferenceType;let G;U?(G=Bh(z,U==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):G=Pa(z);const ne=this.stack[this.stack.length-1];ne.value+=G}function St(P){const z=this.stack.pop();z.position.end=Qt(P.end)}function Wt(P){b.call(this,P);const z=this.stack[this.stack.length-1];z.url=this.sliceSerialize(P)}function Dn(P){b.call(this,P);const z=this.stack[this.stack.length-1];z.url="mailto:"+this.sliceSerialize(P)}function Fn(){return{type:"blockquote",children:[]}}function Pi(){return{type:"code",lang:null,meta:null,value:""}}function og(){return{type:"inlineCode",value:""}}function sg(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function ug(){return{type:"emphasis",children:[]}}function Ha(){return{type:"heading",depth:0,children:[]}}function $a(){return{type:"break"}}function Va(){return{type:"html",value:""}}function ag(){return{type:"image",title:null,url:"",alt:null}}function Wa(){return{type:"link",title:null,url:"",children:[]}}function qa(P){return{type:"list",ordered:P.type==="listOrdered",start:null,spread:P._spread,children:[]}}function cg(P){return{type:"listItem",spread:P._spread,checked:null,children:[]}}function fg(){return{type:"paragraph",children:[]}}function pg(){return{type:"strong",children:[]}}function dg(){return{type:"text",value:""}}function hg(){return{type:"thematicBreak"}}}function Qt(e){return{line:e.line,column:e.column,offset:e.offset}}function Jh(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Jh(e,r):Yk(e,r)}}function Yk(e,t){let n;for(n in t)if(Xh.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Ef(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Kr({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Kr({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Kr({start:t.start,end:t.end})+") is still open")}function Zk(e){const t=this;t.parser=n;function n(r){return Xk(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function ew(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function tw(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function nw(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i=e.applyData(t,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(t,i),i}function rw(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function iw(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function lw(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),i=wr(r.toLowerCase()),l=e.footnoteOrder.indexOf(r);let o,s=e.footnoteCounts.get(r);s===void 0?(s=0,e.footnoteOrder.push(r),o=e.footnoteOrder.length):o=l+1,s+=1,e.footnoteCounts.set(r,s);const u={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(o)}]};e.patch(t,u);const a={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(t,a),e.applyData(t,a)}function ow(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function sw(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function Yh(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const i=e.all(t),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const o=i[i.length-1];return o&&o.type==="text"?o.value+=r:i.push({type:"text",value:r}),i}function uw(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Yh(e,t);const i={src:wr(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)}function aw(e,t){const n={src:wr(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function cw(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function fw(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Yh(e,t);const i={href:wr(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)}function pw(e,t){const n={href:wr(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function dw(e,t,n){const r=e.all(t),i=n?hw(n):Zh(t),l={},o=[];if(typeof t.checked=="boolean"){const c=r[0];let f;c&&c.type==="element"&&c.tagName==="p"?f=c:(f={type:"element",tagName:"p",properties:{},children:[]},r.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const c=r[s];(i||s!==0||c.type!=="element"||c.tagName!=="p")&&o.push({type:"text",value:`
`}),c.type==="element"&&c.tagName==="p"&&!i?o.push(...c.children):o.push(c)}const u=r[r.length-1];u&&(i||u.type!=="element"||u.tagName!=="p")&&o.push({type:"text",value:`
`});const a={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,a),e.applyData(t,a)}function hw(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Zh(n[r])}return t}function Zh(e){const t=e.spread;return t??e.children.length>1}function mw(e,t){const n={},r=e.all(t);let i=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++i<r.length;){const o=r[i];if(o.type==="element"&&o.tagName==="li"&&o.properties&&Array.isArray(o.properties.className)&&o.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)}function gw(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function yw(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function xw(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function kw(e,t){const n=e.all(t),r=n.shift(),i=[];if(r){const o={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],o),i.push(o)}if(n.length>0){const o={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},s=Ca(t.children[1]),u=Ih(t.children[t.children.length-1]);s&&u&&(o.position={start:s,end:u}),i.push(o)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)}function ww(e,t,n){const r=n?n.children:void 0,l=(r?r.indexOf(t):1)===0?"th":"td",o=n&&n.type==="table"?n.align:void 0,s=o?o.length:t.children.length;let u=-1;const a=[];for(;++u<s;){const f=t.children[u],p={},d=o?o[u]:void 0;d&&(p.align=d);let h={type:"element",tagName:l,properties:p,children:[]};f&&(h.children=e.all(f),e.patch(f,h),h=e.applyData(f,h)),a.push(h)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(a,!0)};return e.patch(t,c),e.applyData(t,c)}function vw(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const bf=9,Tf=32;function Sw(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),i=0;const l=[];for(;r;)l.push(Rf(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(Rf(t.slice(i),i>0,!1)),l.join("")}function Rf(e,t,n){let r=0,i=e.length;if(t){let l=e.codePointAt(r);for(;l===bf||l===Tf;)r++,l=e.codePointAt(r)}if(n){let l=e.codePointAt(i-1);for(;l===bf||l===Tf;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function Cw(e,t){const n={type:"text",value:Sw(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function Ew(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const bw={blockquote:ew,break:tw,code:nw,delete:rw,emphasis:iw,footnoteReference:lw,heading:ow,html:sw,imageReference:uw,image:aw,inlineCode:cw,linkReference:fw,link:pw,listItem:dw,list:mw,paragraph:gw,root:yw,strong:xw,table:kw,tableCell:vw,tableRow:ww,text:Cw,thematicBreak:Ew,toml:qi,yaml:qi,definition:qi,footnoteDefinition:qi};function qi(){}const em=-1,co=0,Jr=1,Ml=2,Aa=3,La=4,Ia=5,za=6,tm=7,nm=8,Pf=typeof self=="object"?self:globalThis,Tw=(e,t)=>{const n=(i,l)=>(e.set(l,i),i),r=i=>{if(e.has(i))return e.get(i);const[l,o]=t[i];switch(l){case co:case em:return n(o,i);case Jr:{const s=n([],i);for(const u of o)s.push(r(u));return s}case Ml:{const s=n({},i);for(const[u,a]of o)s[r(u)]=r(a);return s}case Aa:return n(new Date(o),i);case La:{const{source:s,flags:u}=o;return n(new RegExp(s,u),i)}case Ia:{const s=n(new Map,i);for(const[u,a]of o)s.set(r(u),r(a));return s}case za:{const s=n(new Set,i);for(const u of o)s.add(r(u));return s}case tm:{const{name:s,message:u}=o;return n(new Pf[s](u),i)}case nm:return n(BigInt(o),i);case"BigInt":return n(Object(BigInt(o)),i);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{const{buffer:s}=new Uint8Array(o);return n(new DataView(s),o)}}return n(new Pf[l](o),i)};return r},_f=e=>Tw(new Map,e)(0),Un="",{toString:Rw}={},{keys:Pw}=Object,Or=e=>{const t=typeof e;if(t!=="object"||!e)return[co,t];const n=Rw.call(e).slice(8,-1);switch(n){case"Array":return[Jr,Un];case"Object":return[Ml,Un];case"Date":return[Aa,Un];case"RegExp":return[La,Un];case"Map":return[Ia,Un];case"Set":return[za,Un];case"DataView":return[Jr,n]}return n.includes("Array")?[Jr,n]:n.includes("Error")?[tm,n]:[Ml,n]},Qi=([e,t])=>e===co&&(t==="function"||t==="symbol"),_w=(e,t,n,r)=>{const i=(o,s)=>{const u=r.push(o)-1;return n.set(s,u),u},l=o=>{if(n.has(o))return n.get(o);let[s,u]=Or(o);switch(s){case co:{let c=o;switch(u){case"bigint":s=nm,c=o.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+u);c=null;break;case"undefined":return i([em],o)}return i([s,c],o)}case Jr:{if(u){let p=o;return u==="DataView"?p=new Uint8Array(o.buffer):u==="ArrayBuffer"&&(p=new Uint8Array(o)),i([u,[...p]],o)}const c=[],f=i([s,c],o);for(const p of o)c.push(l(p));return f}case Ml:{if(u)switch(u){case"BigInt":return i([u,o.toString()],o);case"Boolean":case"Number":case"String":return i([u,o.valueOf()],o)}if(t&&"toJSON"in o)return l(o.toJSON());const c=[],f=i([s,c],o);for(const p of Pw(o))(e||!Qi(Or(o[p])))&&c.push([l(p),l(o[p])]);return f}case Aa:return i([s,o.toISOString()],o);case La:{const{source:c,flags:f}=o;return i([s,{source:c,flags:f}],o)}case Ia:{const c=[],f=i([s,c],o);for(const[p,d]of o)(e||!(Qi(Or(p))||Qi(Or(d))))&&c.push([l(p),l(d)]);return f}case za:{const c=[],f=i([s,c],o);for(const p of o)(e||!Qi(Or(p)))&&c.push(l(p));return f}}const{message:a}=o;return i([s,{name:u,message:a}],o)};return l},Af=(e,{json:t,lossy:n}={})=>{const r=[];return _w(!(t||n),!!t,new Map,r)(e),r},Bl=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?_f(Af(e,t)):structuredClone(e):(e,t)=>_f(Af(e,t));function Aw(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function Lw(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function Iw(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||Aw,r=e.options.footnoteBackLabel||Lw,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let u=-1;for(;++u<e.footnoteOrder.length;){const a=e.footnoteById.get(e.footnoteOrder[u]);if(!a)continue;const c=e.all(a),f=String(a.identifier).toUpperCase(),p=wr(f.toLowerCase());let d=0;const h=[],x=e.footnoteCounts.get(f);for(;x!==void 0&&++d<=x;){h.length>0&&h.push({type:"text",value:" "});let g=typeof n=="string"?n:n(u,d);typeof g=="string"&&(g={type:"text",value:g}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+p+(d>1?"-"+d:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(u,d),className:["data-footnote-backref"]},children:Array.isArray(g)?g:[g]})}const E=c[c.length-1];if(E&&E.type==="element"&&E.tagName==="p"){const g=E.children[E.children.length-1];g&&g.type==="text"?g.value+=" ":E.children.push({type:"text",value:" "}),E.children.push(...h)}else c.push(...h);const m={type:"element",tagName:"li",properties:{id:t+"fn-"+p},children:e.wrap(c,!0)};e.patch(a,m),s.push(m)}if(s.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...Bl(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:`
`}]}}const fo=function(e){if(e==null)return Nw;if(typeof e=="function")return po(e);if(typeof e=="object")return Array.isArray(e)?zw(e):Ow(e);if(typeof e=="string")return jw(e);throw new Error("Expected function, string, or object as test")};function zw(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=fo(e[n]);return po(r);function r(...i){let l=-1;for(;++l<t.length;)if(t[l].apply(this,i))return!0;return!1}}function Ow(e){const t=e;return po(n);function n(r){const i=r;let l;for(l in e)if(i[l]!==t[l])return!1;return!0}}function jw(e){return po(t);function t(n){return n&&n.type===e}}function po(e){return t;function t(n,r,i){return!!(Dw(n)&&e.call(this,n,typeof r=="number"?r:void 0,i||void 0))}}function Nw(){return!0}function Dw(e){return e!==null&&typeof e=="object"&&"type"in e}const rm=[],Fw=!0,fu=!1,Mw="skip";function im(e,t,n,r){let i;typeof t=="function"&&typeof n!="function"?(r=n,n=t):i=t;const l=fo(i),o=r?-1:1;s(e,void 0,[])();function s(u,a,c){const f=u&&typeof u=="object"?u:{};if(typeof f.type=="string"){const d=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(u.type+(d?"<"+d+">":""))+")"})}return p;function p(){let d=rm,h,x,E;if((!t||l(u,a,c[c.length-1]||void 0))&&(d=Bw(n(u,c)),d[0]===fu))return d;if("children"in u&&u.children){const m=u;if(m.children&&d[0]!==Mw)for(x=(r?m.children.length:-1)+o,E=c.concat(m);x>-1&&x<m.children.length;){const g=m.children[x];if(h=s(g,x,E)(),h[0]===fu)return h;x=typeof h[1]=="number"?h[1]:x+o}}return d}}}function Bw(e){return Array.isArray(e)?e:typeof e=="number"?[Fw,e]:e==null?rm:[e]}function Oa(e,t,n,r){let i,l,o;typeof t=="function"?(l=void 0,o=t,i=n):(l=t,o=n,i=r),im(e,l,s,i);function s(u,a){const c=a[a.length-1],f=c?c.children.indexOf(u):void 0;return o(u,f,c)}}const pu={}.hasOwnProperty,Uw={};function Hw(e,t){const n=t||Uw,r=new Map,i=new Map,l=new Map,o={...bw,...n.handlers},s={all:a,applyData:Vw,definitionById:r,footnoteById:i,footnoteCounts:l,footnoteOrder:[],handlers:o,one:u,options:n,patch:$w,wrap:qw};return Oa(e,function(c){if(c.type==="definition"||c.type==="footnoteDefinition"){const f=c.type==="definition"?r:i,p=String(c.identifier).toUpperCase();f.has(p)||f.set(p,c)}}),s;function u(c,f){const p=c.type,d=s.handlers[p];if(pu.call(s.handlers,p)&&d)return d(s,c,f);if(s.options.passThrough&&s.options.passThrough.includes(p)){if("children"in c){const{children:x,...E}=c,m=Bl(E);return m.children=s.all(c),m}return Bl(c)}return(s.options.unknownHandler||Ww)(s,c,f)}function a(c){const f=[];if("children"in c){const p=c.children;let d=-1;for(;++d<p.length;){const h=s.one(p[d],c);if(h){if(d&&p[d-1].type==="break"&&(!Array.isArray(h)&&h.type==="text"&&(h.value=Lf(h.value)),!Array.isArray(h)&&h.type==="element")){const x=h.children[0];x&&x.type==="text"&&(x.value=Lf(x.value))}Array.isArray(h)?f.push(...h):f.push(h)}}}return f}}function $w(e,t){e.position&&(t.position=L1(e))}function Vw(e,t){let n=t;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const o="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:o}}n.type==="element"&&l&&Object.assign(n.properties,Bl(l)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function Ww(e,t){const n=t.data||{},r="value"in t&&!(pu.call(n,"hProperties")||pu.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function qw(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function Lf(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function If(e,t){const n=Hw(e,t),r=n.one(e,void 0),i=Iw(n),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&l.children.push({type:"text",value:`
`},i),l}function Qw(e,t){return e&&"run"in e?async function(n,r){const i=If(n,{file:r,...t});await e.run(i,r)}:function(n,r){return If(n,{file:r,...e||t})}}function zf(e){if(e)throw e}var al=Object.prototype.hasOwnProperty,lm=Object.prototype.toString,Of=Object.defineProperty,jf=Object.getOwnPropertyDescriptor,Nf=function(t){return typeof Array.isArray=="function"?Array.isArray(t):lm.call(t)==="[object Array]"},Df=function(t){if(!t||lm.call(t)!=="[object Object]")return!1;var n=al.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&al.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var i;for(i in t);return typeof i>"u"||al.call(t,i)},Ff=function(t,n){Of&&n.name==="__proto__"?Of(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},Mf=function(t,n){if(n==="__proto__")if(al.call(t,n)){if(jf)return jf(t,n).value}else return;return t[n]},Gw=function e(){var t,n,r,i,l,o,s=arguments[0],u=1,a=arguments.length,c=!1;for(typeof s=="boolean"&&(c=s,s=arguments[1]||{},u=2),(s==null||typeof s!="object"&&typeof s!="function")&&(s={});u<a;++u)if(t=arguments[u],t!=null)for(n in t)r=Mf(s,n),i=Mf(t,n),s!==i&&(c&&i&&(Df(i)||(l=Nf(i)))?(l?(l=!1,o=r&&Nf(r)?r:[]):o=r&&Df(r)?r:{},Ff(s,{name:n,newValue:e(c,o,i)})):typeof i<"u"&&Ff(s,{name:n,newValue:i}));return s};const Jo=Eu(Gw);function du(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Kw(){const e=[],t={run:n,use:r};return t;function n(...i){let l=-1;const o=i.pop();if(typeof o!="function")throw new TypeError("Expected function as last argument, not "+o);s(null,...i);function s(u,...a){const c=e[++l];let f=-1;if(u){o(u);return}for(;++f<i.length;)(a[f]===null||a[f]===void 0)&&(a[f]=i[f]);i=a,c?Xw(c,s)(...a):o(null,...a)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function Xw(e,t){let n;return r;function r(...o){const s=e.length>o.length;let u;s&&o.push(i);try{u=e.apply(this,o)}catch(a){const c=a;if(s&&n)throw c;return i(c)}s||(u&&u.then&&typeof u.then=="function"?u.then(l,i):u instanceof Error?i(u):l(u))}function i(o,...s){n||(n=!0,t(o,...s))}function l(o){i(null,o)}}const Tt={basename:Jw,dirname:Yw,extname:Zw,join:ev,sep:"/"};function Jw(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');bi(e);let n=0,r=-1,i=e.length,l;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(l){n=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let o=-1,s=t.length-1;for(;i--;)if(e.codePointAt(i)===47){if(l){n=i+1;break}}else o<0&&(l=!0,o=i+1),s>-1&&(e.codePointAt(i)===t.codePointAt(s--)?s<0&&(r=i):(s=-1,r=o));return n===r?r=o:r<0&&(r=e.length),e.slice(n,r)}function Yw(e){if(bi(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function Zw(e){bi(e);let t=e.length,n=-1,r=0,i=-1,l=0,o;for(;t--;){const s=e.codePointAt(t);if(s===47){if(o){r=t+1;break}continue}n<0&&(o=!0,n=t+1),s===46?i<0?i=t:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||n<0||l===0||l===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function ev(...e){let t=-1,n;for(;++t<e.length;)bi(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":tv(n)}function tv(e){bi(e);const t=e.codePointAt(0)===47;let n=nv(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function nv(e,t){let n="",r=0,i=-1,l=0,o=-1,s,u;for(;++o<=e.length;){if(o<e.length)s=e.codePointAt(o);else{if(s===47)break;s=47}if(s===47){if(!(i===o-1||l===1))if(i!==o-1&&l===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(u=n.lastIndexOf("/"),u!==n.length-1){u<0?(n="",r=0):(n=n.slice(0,u),r=n.length-1-n.lastIndexOf("/")),i=o,l=0;continue}}else if(n.length>0){n="",r=0,i=o,l=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,o):n=e.slice(i+1,o),r=o-i-1;i=o,l=0}else s===46&&l>-1?l++:l=-1}return n}function bi(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const rv={cwd:iv};function iv(){return"/"}function hu(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function lv(e){if(typeof e=="string")e=new URL(e);else if(!hu(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return ov(e)}function ov(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const Yo=["history","path","basename","stem","extname","dirname"];class om{constructor(t){let n;t?hu(t)?n={path:t}:typeof t=="string"||sv(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":rv.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Yo.length;){const l=Yo[r];l in n&&n[l]!==void 0&&n[l]!==null&&(this[l]=l==="history"?[...n[l]]:n[l])}let i;for(i in n)Yo.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Tt.basename(this.path):void 0}set basename(t){es(t,"basename"),Zo(t,"basename"),this.path=Tt.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?Tt.dirname(this.path):void 0}set dirname(t){Bf(this.basename,"dirname"),this.path=Tt.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?Tt.extname(this.path):void 0}set extname(t){if(Zo(t,"extname"),Bf(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Tt.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){hu(t)&&(t=lv(t)),es(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?Tt.basename(this.path,this.extname):void 0}set stem(t){es(t,"stem"),Zo(t,"stem"),this.path=Tt.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=void 0,i}message(t,n,r){const i=new Oe(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Zo(e,t){if(e&&e.includes(Tt.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Tt.sep+"`")}function es(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Bf(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function sv(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const uv=function(e){const r=this.constructor.prototype,i=r[e],l=function(){return i.apply(l,arguments)};return Object.setPrototypeOf(l,r),l},av={}.hasOwnProperty;class ja extends uv{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Kw()}copy(){const t=new ja;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(Jo(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(rs("data",this.frozen),this.namespace[t]=n,this):av.call(this.namespace,t)&&this.namespace[t]||void 0:t?(rs("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=n.call(t,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=Gi(t),r=this.parser||this.Parser;return ts("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),ts("process",this.parser||this.Parser),ns("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(l,o){const s=Gi(t),u=r.parse(s);r.run(u,s,function(c,f,p){if(c||!f||!p)return a(c);const d=f,h=r.stringify(d,p);pv(h)?p.value=h:p.result=h,a(c,p)});function a(c,f){c||!f?o(c):l?l(f):n(void 0,f)}}}processSync(t){let n=!1,r;return this.freeze(),ts("processSync",this.parser||this.Parser),ns("processSync",this.compiler||this.Compiler),this.process(t,i),Hf("processSync","process",n),r;function i(l,o){n=!0,zf(l),r=o}}run(t,n,r){Uf(t),this.freeze();const i=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?l(void 0,r):new Promise(l);function l(o,s){const u=Gi(n);i.run(t,u,a);function a(c,f,p){const d=f||t;c?s(c):o?o(d):r(void 0,d,p)}}}runSync(t,n){let r=!1,i;return this.run(t,n,l),Hf("runSync","run",r),i;function l(o,s){zf(o),i=s,r=!0}}stringify(t,n){this.freeze();const r=Gi(n),i=this.compiler||this.Compiler;return ns("stringify",i),Uf(t),i(t,r)}use(t,...n){const r=this.attachers,i=this.namespace;if(rs("use",this.frozen),t!=null)if(typeof t=="function")u(t,n);else if(typeof t=="object")Array.isArray(t)?s(t):o(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function l(a){if(typeof a=="function")u(a,[]);else if(typeof a=="object")if(Array.isArray(a)){const[c,...f]=a;u(c,f)}else o(a);else throw new TypeError("Expected usable value, not `"+a+"`")}function o(a){if(!("plugins"in a)&&!("settings"in a))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");s(a.plugins),a.settings&&(i.settings=Jo(!0,i.settings,a.settings))}function s(a){let c=-1;if(a!=null)if(Array.isArray(a))for(;++c<a.length;){const f=a[c];l(f)}else throw new TypeError("Expected a list of plugins, not `"+a+"`")}function u(a,c){let f=-1,p=-1;for(;++f<r.length;)if(r[f][0]===a){p=f;break}if(p===-1)r.push([a,...c]);else if(c.length>0){let[d,...h]=c;const x=r[p][1];du(x)&&du(d)&&(d=Jo(!0,x,d)),r[p]=[a,d,...h]}}}}const cv=new ja().freeze();function ts(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function ns(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function rs(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Uf(e){if(!du(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Hf(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Gi(e){return fv(e)?e:new om(e)}function fv(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function pv(e){return typeof e=="string"||dv(e)}function dv(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const hv="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",$f=[],Vf={allowDangerousHtml:!0},mv=/^(https?|ircs?|mailto|xmpp)$/i,gv=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function sm(e){const t=yv(e),n=xv(e);return kv(t.runSync(t.parse(n),n),e)}function yv(e){const t=e.rehypePlugins||$f,n=e.remarkPlugins||$f,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Vf}:Vf;return cv().use(Zk).use(n).use(Qw,r).use(t)}function xv(e){const t=e.children||"",n=new om;return typeof t=="string"&&(n.value=t),n}function kv(e,t){const n=t.allowedElements,r=t.allowElement,i=t.components,l=t.disallowedElements,o=t.skipHtml,s=t.unwrapDisallowed,u=t.urlTransform||wv;for(const c of gv)Object.hasOwn(t,c.from)&&(""+c.from+(c.to?"use `"+c.to+"` instead":"remove it")+hv+c.id,void 0);return Oa(e,a),N1(e,{Fragment:w.Fragment,components:i,ignoreInvalidStyle:!0,jsx:w.jsx,jsxs:w.jsxs,passKeys:!0,passNode:!0});function a(c,f,p){if(c.type==="raw"&&p&&typeof f=="number")return o?p.children.splice(f,1):p.children[f]={type:"text",value:c.value},f;if(c.type==="element"){let d;for(d in Go)if(Object.hasOwn(Go,d)&&Object.hasOwn(c.properties,d)){const h=c.properties[d],x=Go[d];(x===null||x.includes(c.tagName))&&(c.properties[d]=u(String(h||""),d,c))}}if(c.type==="element"){let d=n?!n.includes(c.tagName):l?l.includes(c.tagName):!1;if(!d&&r&&typeof f=="number"&&(d=!r(c,f,p)),d&&p&&typeof f=="number")return s&&c.children?p.children.splice(f,1,...c.children):p.children.splice(f,1),f}}}function wv(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return t===-1||i!==-1&&t>i||n!==-1&&t>n||r!==-1&&t>r||mv.test(e.slice(0,t))?e:""}function Wf(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,i=n.indexOf(t);for(;i!==-1;)r++,i=n.indexOf(t,i+t.length);return r}function vv(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function Sv(e,t,n){const i=fo((n||{}).ignore||[]),l=Cv(t);let o=-1;for(;++o<l.length;)im(e,"text",s);function s(a,c){let f=-1,p;for(;++f<c.length;){const d=c[f],h=p?p.children:void 0;if(i(d,h?h.indexOf(d):void 0,p))return;p=d}if(p)return u(a,c)}function u(a,c){const f=c[c.length-1],p=l[o][0],d=l[o][1];let h=0;const E=f.children.indexOf(a);let m=!1,g=[];p.lastIndex=0;let y=p.exec(a.value);for(;y;){const v=y.index,T={index:y.index,input:y.input,stack:[...c,a]};let C=d(...y,T);if(typeof C=="string"&&(C=C.length>0?{type:"text",value:C}:void 0),C===!1?p.lastIndex=v+1:(h!==v&&g.push({type:"text",value:a.value.slice(h,v)}),Array.isArray(C)?g.push(...C):C&&g.push(C),h=v+y[0].length,m=!0),!p.global)break;y=p.exec(a.value)}return m?(h<a.value.length&&g.push({type:"text",value:a.value.slice(h)}),f.children.splice(E,1,...g)):g=[a],E+g.length}}function Cv(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const i=n[r];t.push([Ev(i[0]),bv(i[1])])}return t}function Ev(e){return typeof e=="string"?new RegExp(vv(e),"g"):e}function bv(e){return typeof e=="function"?e:function(){return e}}const is="phrasing",ls=["autolink","link","image","label"];function Tv(){return{transforms:[zv],enter:{literalAutolink:Pv,literalAutolinkEmail:os,literalAutolinkHttp:os,literalAutolinkWww:os},exit:{literalAutolink:Iv,literalAutolinkEmail:Lv,literalAutolinkHttp:_v,literalAutolinkWww:Av}}}function Rv(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:is,notInConstruct:ls},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:is,notInConstruct:ls},{character:":",before:"[ps]",after:"\\/",inConstruct:is,notInConstruct:ls}]}}function Pv(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function os(e){this.config.enter.autolinkProtocol.call(this,e)}function _v(e){this.config.exit.autolinkProtocol.call(this,e)}function Av(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function Lv(e){this.config.exit.autolinkEmail.call(this,e)}function Iv(e){this.exit(e)}function zv(e){Sv(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,Ov],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),jv]],{ignore:["link","linkReference"]})}function Ov(e,t,n,r,i){let l="";if(!um(i)||(/^w/i.test(t)&&(n=t+n,t="",l="http://"),!Nv(n)))return!1;const o=Dv(n+r);if(!o[0])return!1;const s={type:"link",title:null,url:l+t+o[0],children:[{type:"text",value:t+o[0]}]};return o[1]?[s,{type:"text",value:o[1]}]:s}function jv(e,t,n,r){return!um(r,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function Nv(e){const t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}function Dv(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const i=Wf(e,"(");let l=Wf(e,")");for(;r!==-1&&i>l;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),l++;return[e,n]}function um(e,t){const n=e.input.charCodeAt(e.index-1);return(e.index===0||In(n)||uo(n))&&(!t||n!==47)}am.peek=qv;function Fv(){this.buffer()}function Mv(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function Bv(){this.buffer()}function Uv(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function Hv(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=kt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function $v(e){this.exit(e)}function Vv(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=kt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Wv(e){this.exit(e)}function qv(){return"["}function am(e,t,n,r){const i=n.createTracker(r);let l=i.move("[^");const o=n.enter("footnoteReference"),s=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),s(),o(),l+=i.move("]"),l}function Qv(){return{enter:{gfmFootnoteCallString:Fv,gfmFootnoteCall:Mv,gfmFootnoteDefinitionLabelString:Bv,gfmFootnoteDefinition:Uv},exit:{gfmFootnoteCallString:Hv,gfmFootnoteCall:$v,gfmFootnoteDefinitionLabelString:Vv,gfmFootnoteDefinition:Wv}}}function Gv(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:n,footnoteReference:am},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(r,i,l,o){const s=l.createTracker(o);let u=s.move("[^");const a=l.enter("footnoteDefinition"),c=l.enter("label");return u+=s.move(l.safe(l.associationId(r),{before:u,after:"]"})),c(),u+=s.move("]:"),r.children&&r.children.length>0&&(s.shift(4),u+=s.move((t?`
`:" ")+l.indentLines(l.containerFlow(r,s.current()),t?cm:Kv))),a(),u}}function Kv(e,t,n){return t===0?e:cm(e,t,n)}function cm(e,t,n){return(n?"":"    ")+e}const Xv=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];fm.peek=tS;function Jv(){return{canContainEols:["delete"],enter:{strikethrough:Zv},exit:{strikethrough:eS}}}function Yv(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:Xv}],handlers:{delete:fm}}}function Zv(e){this.enter({type:"delete",children:[]},e)}function eS(e){this.exit(e)}function fm(e,t,n,r){const i=n.createTracker(r),l=n.enter("strikethrough");let o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"}),o+=i.move("~~"),l(),o}function tS(){return"~"}function nS(e){return e.length}function rS(e,t){const n=t||{},r=(n.align||[]).concat(),i=n.stringLength||nS,l=[],o=[],s=[],u=[];let a=0,c=-1;for(;++c<e.length;){const x=[],E=[];let m=-1;for(e[c].length>a&&(a=e[c].length);++m<e[c].length;){const g=iS(e[c][m]);if(n.alignDelimiters!==!1){const y=i(g);E[m]=y,(u[m]===void 0||y>u[m])&&(u[m]=y)}x.push(g)}o[c]=x,s[c]=E}let f=-1;if(typeof r=="object"&&"length"in r)for(;++f<a;)l[f]=qf(r[f]);else{const x=qf(r);for(;++f<a;)l[f]=x}f=-1;const p=[],d=[];for(;++f<a;){const x=l[f];let E="",m="";x===99?(E=":",m=":"):x===108?E=":":x===114&&(m=":");let g=n.alignDelimiters===!1?1:Math.max(1,u[f]-E.length-m.length);const y=E+"-".repeat(g)+m;n.alignDelimiters!==!1&&(g=E.length+g+m.length,g>u[f]&&(u[f]=g),d[f]=g),p[f]=y}o.splice(1,0,p),s.splice(1,0,d),c=-1;const h=[];for(;++c<o.length;){const x=o[c],E=s[c];f=-1;const m=[];for(;++f<a;){const g=x[f]||"";let y="",v="";if(n.alignDelimiters!==!1){const T=u[f]-(E[f]||0),C=l[f];C===114?y=" ".repeat(T):C===99?T%2?(y=" ".repeat(T/2+.5),v=" ".repeat(T/2-.5)):(y=" ".repeat(T/2),v=y):v=" ".repeat(T)}n.delimiterStart!==!1&&!f&&m.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&g==="")&&(n.delimiterStart!==!1||f)&&m.push(" "),n.alignDelimiters!==!1&&m.push(y),m.push(g),n.alignDelimiters!==!1&&m.push(v),n.padding!==!1&&m.push(" "),(n.delimiterEnd!==!1||f!==a-1)&&m.push("|")}h.push(n.delimiterEnd===!1?m.join("").replace(/ +$/,""):m.join(""))}return h.join(`
`)}function iS(e){return e==null?"":String(e)}function qf(e){const t=typeof e=="string"?e.codePointAt(0):0;return t===67||t===99?99:t===76||t===108?108:t===82||t===114?114:0}function lS(e,t,n,r){const i=n.enter("blockquote"),l=n.createTracker(r);l.move("> "),l.shift(2);const o=n.indentLines(n.containerFlow(e,l.current()),oS);return i(),o}function oS(e,t,n){return">"+(n?"":" ")+e}function sS(e,t){return Qf(e,t.inConstruct,!0)&&!Qf(e,t.notInConstruct,!1)}function Qf(e,t,n){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Gf(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&sS(n.stack,n.unsafe[i]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function uS(e,t){const n=String(e);let r=n.indexOf(t),i=r,l=0,o=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===i?++l>o&&(o=l):l=1,i=r+t.length,r=n.indexOf(t,i);return o}function aS(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}function cS(e){const t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}function fS(e,t,n,r){const i=cS(n),l=e.value||"",o=i==="`"?"GraveAccent":"Tilde";if(aS(e,n)){const f=n.enter("codeIndented"),p=n.indentLines(l,pS);return f(),p}const s=n.createTracker(r),u=i.repeat(Math.max(uS(l,i)+1,3)),a=n.enter("codeFenced");let c=s.move(u);if(e.lang){const f=n.enter(`codeFencedLang${o}`);c+=s.move(n.safe(e.lang,{before:c,after:" ",encode:["`"],...s.current()})),f()}if(e.lang&&e.meta){const f=n.enter(`codeFencedMeta${o}`);c+=s.move(" "),c+=s.move(n.safe(e.meta,{before:c,after:`
`,encode:["`"],...s.current()})),f()}return c+=s.move(`
`),l&&(c+=s.move(l+`
`)),c+=s.move(u),a(),c}function pS(e,t,n){return(n?"":"    ")+e}function Na(e){const t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function dS(e,t,n,r){const i=Na(n),l=i==='"'?"Quote":"Apostrophe",o=n.enter("definition");let s=n.enter("label");const u=n.createTracker(r);let a=u.move("[");return a+=u.move(n.safe(n.associationId(e),{before:a,after:"]",...u.current()})),a+=u.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),a+=u.move("<"),a+=u.move(n.safe(e.url,{before:a,after:">",...u.current()})),a+=u.move(">")):(s=n.enter("destinationRaw"),a+=u.move(n.safe(e.url,{before:a,after:e.title?" ":`
`,...u.current()}))),s(),e.title&&(s=n.enter(`title${l}`),a+=u.move(" "+i),a+=u.move(n.safe(e.title,{before:a,after:i,...u.current()})),a+=u.move(i),s()),o(),a}function hS(e){const t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}function gi(e){return"&#x"+e.toString(16).toUpperCase()+";"}function Ul(e,t,n){const r=mr(e),i=mr(t);return r===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:r===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}pm.peek=mS;function pm(e,t,n,r){const i=hS(n),l=n.enter("emphasis"),o=n.createTracker(r),s=o.move(i);let u=o.move(n.containerPhrasing(e,{after:i,before:s,...o.current()}));const a=u.charCodeAt(0),c=Ul(r.before.charCodeAt(r.before.length-1),a,i);c.inside&&(u=gi(a)+u.slice(1));const f=u.charCodeAt(u.length-1),p=Ul(r.after.charCodeAt(0),f,i);p.inside&&(u=u.slice(0,-1)+gi(f));const d=o.move(i);return l(),n.attentionEncodeSurroundingInfo={after:p.outside,before:c.outside},s+u+d}function mS(e,t,n){return n.options.emphasis||"*"}function gS(e,t){let n=!1;return Oa(e,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return n=!0,fu}),!!((!e.depth||e.depth<3)&&Ra(e)&&(t.options.setext||n))}function yS(e,t,n,r){const i=Math.max(Math.min(6,e.depth||1),1),l=n.createTracker(r);if(gS(e,n)){const c=n.enter("headingSetext"),f=n.enter("phrasing"),p=n.containerPhrasing(e,{...l.current(),before:`
`,after:`
`});return f(),c(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}const o="#".repeat(i),s=n.enter("headingAtx"),u=n.enter("phrasing");l.move(o+" ");let a=n.containerPhrasing(e,{before:"# ",after:`
`,...l.current()});return/^[\t ]/.test(a)&&(a=gi(a.charCodeAt(0))+a.slice(1)),a=a?o+" "+a:o,n.options.closeAtx&&(a+=" "+o),u(),s(),a}dm.peek=xS;function dm(e){return e.value||""}function xS(){return"<"}hm.peek=kS;function hm(e,t,n,r){const i=Na(n),l=i==='"'?"Quote":"Apostrophe",o=n.enter("image");let s=n.enter("label");const u=n.createTracker(r);let a=u.move("![");return a+=u.move(n.safe(e.alt,{before:a,after:"]",...u.current()})),a+=u.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),a+=u.move("<"),a+=u.move(n.safe(e.url,{before:a,after:">",...u.current()})),a+=u.move(">")):(s=n.enter("destinationRaw"),a+=u.move(n.safe(e.url,{before:a,after:e.title?" ":")",...u.current()}))),s(),e.title&&(s=n.enter(`title${l}`),a+=u.move(" "+i),a+=u.move(n.safe(e.title,{before:a,after:i,...u.current()})),a+=u.move(i),s()),a+=u.move(")"),o(),a}function kS(){return"!"}mm.peek=wS;function mm(e,t,n,r){const i=e.referenceType,l=n.enter("imageReference");let o=n.enter("label");const s=n.createTracker(r);let u=s.move("![");const a=n.safe(e.alt,{before:u,after:"]",...s.current()});u+=s.move(a+"]["),o();const c=n.stack;n.stack=[],o=n.enter("reference");const f=n.safe(n.associationId(e),{before:u,after:"]",...s.current()});return o(),n.stack=c,l(),i==="full"||!a||a!==f?u+=s.move(f+"]"):i==="shortcut"?u=u.slice(0,-1):u+=s.move("]"),u}function wS(){return"!"}gm.peek=vS;function gm(e,t,n){let r=e.value||"",i="`",l=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){const o=n.unsafe[l],s=n.compilePattern(o);let u;if(o.atBreak)for(;u=s.exec(r);){let a=u.index;r.charCodeAt(a)===10&&r.charCodeAt(a-1)===13&&a--,r=r.slice(0,a)+" "+r.slice(u.index+1)}}return i+r+i}function vS(){return"`"}function ym(e,t){const n=Ra(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}xm.peek=SS;function xm(e,t,n,r){const i=Na(n),l=i==='"'?"Quote":"Apostrophe",o=n.createTracker(r);let s,u;if(ym(e,n)){const c=n.stack;n.stack=[],s=n.enter("autolink");let f=o.move("<");return f+=o.move(n.containerPhrasing(e,{before:f,after:">",...o.current()})),f+=o.move(">"),s(),n.stack=c,f}s=n.enter("link"),u=n.enter("label");let a=o.move("[");return a+=o.move(n.containerPhrasing(e,{before:a,after:"](",...o.current()})),a+=o.move("]("),u(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(u=n.enter("destinationLiteral"),a+=o.move("<"),a+=o.move(n.safe(e.url,{before:a,after:">",...o.current()})),a+=o.move(">")):(u=n.enter("destinationRaw"),a+=o.move(n.safe(e.url,{before:a,after:e.title?" ":")",...o.current()}))),u(),e.title&&(u=n.enter(`title${l}`),a+=o.move(" "+i),a+=o.move(n.safe(e.title,{before:a,after:i,...o.current()})),a+=o.move(i),u()),a+=o.move(")"),s(),a}function SS(e,t,n){return ym(e,n)?"<":"["}km.peek=CS;function km(e,t,n,r){const i=e.referenceType,l=n.enter("linkReference");let o=n.enter("label");const s=n.createTracker(r);let u=s.move("[");const a=n.containerPhrasing(e,{before:u,after:"]",...s.current()});u+=s.move(a+"]["),o();const c=n.stack;n.stack=[],o=n.enter("reference");const f=n.safe(n.associationId(e),{before:u,after:"]",...s.current()});return o(),n.stack=c,l(),i==="full"||!a||a!==f?u+=s.move(f+"]"):i==="shortcut"?u=u.slice(0,-1):u+=s.move("]"),u}function CS(){return"["}function Da(e){const t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function ES(e){const t=Da(e),n=e.options.bulletOther;if(!n)return t==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}function bS(e){const t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}function wm(e){const t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}function TS(e,t,n,r){const i=n.enter("list"),l=n.bulletCurrent;let o=e.ordered?bS(n):Da(n);const s=e.ordered?o==="."?")":".":ES(n);let u=t&&n.bulletLastUsed?o===n.bulletLastUsed:!1;if(!e.ordered){const c=e.children?e.children[0]:void 0;if((o==="*"||o==="-")&&c&&(!c.children||!c.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(u=!0),wm(n)===o&&c){let f=-1;for(;++f<e.children.length;){const p=e.children[f];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){u=!0;break}}}}u&&(o=s),n.bulletCurrent=o;const a=n.containerFlow(e,r);return n.bulletLastUsed=o,n.bulletCurrent=l,i(),a}function RS(e){const t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}function PS(e,t,n,r){const i=RS(n);let l=n.bulletCurrent||Da(n);t&&t.type==="list"&&t.ordered&&(l=(typeof t.start=="number"&&t.start>-1?t.start:1)+(n.options.incrementListMarker===!1?0:t.children.indexOf(e))+l);let o=l.length+1;(i==="tab"||i==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(o=Math.ceil(o/4)*4);const s=n.createTracker(r);s.move(l+" ".repeat(o-l.length)),s.shift(o);const u=n.enter("listItem"),a=n.indentLines(n.containerFlow(e,s.current()),c);return u(),a;function c(f,p,d){return p?(d?"":" ".repeat(o))+f:(d?l:l+" ".repeat(o-l.length))+f}}function _S(e,t,n,r){const i=n.enter("paragraph"),l=n.enter("phrasing"),o=n.containerPhrasing(e,r);return l(),i(),o}const AS=fo(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function LS(e,t,n,r){return(e.children.some(function(o){return AS(o)})?n.containerPhrasing:n.containerFlow).call(n,e,r)}function IS(e){const t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}vm.peek=zS;function vm(e,t,n,r){const i=IS(n),l=n.enter("strong"),o=n.createTracker(r),s=o.move(i+i);let u=o.move(n.containerPhrasing(e,{after:i,before:s,...o.current()}));const a=u.charCodeAt(0),c=Ul(r.before.charCodeAt(r.before.length-1),a,i);c.inside&&(u=gi(a)+u.slice(1));const f=u.charCodeAt(u.length-1),p=Ul(r.after.charCodeAt(0),f,i);p.inside&&(u=u.slice(0,-1)+gi(f));const d=o.move(i+i);return l(),n.attentionEncodeSurroundingInfo={after:p.outside,before:c.outside},s+u+d}function zS(e,t,n){return n.options.strong||"*"}function OS(e,t,n,r){return n.safe(e.value,r)}function jS(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}function NS(e,t,n){const r=(wm(n)+(n.options.ruleSpaces?" ":"")).repeat(jS(n));return n.options.ruleSpaces?r.slice(0,-1):r}const Sm={blockquote:lS,break:Gf,code:fS,definition:dS,emphasis:pm,hardBreak:Gf,heading:yS,html:dm,image:hm,imageReference:mm,inlineCode:gm,link:xm,linkReference:km,list:TS,listItem:PS,paragraph:_S,root:LS,strong:vm,text:OS,thematicBreak:NS};function DS(){return{enter:{table:FS,tableData:Kf,tableHeader:Kf,tableRow:BS},exit:{codeText:US,table:MS,tableData:ss,tableHeader:ss,tableRow:ss}}}function FS(e){const t=e._align;this.enter({type:"table",align:t.map(function(n){return n==="none"?null:n}),children:[]},e),this.data.inTable=!0}function MS(e){this.exit(e),this.data.inTable=void 0}function BS(e){this.enter({type:"tableRow",children:[]},e)}function ss(e){this.exit(e)}function Kf(e){this.enter({type:"tableCell",children:[]},e)}function US(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,HS));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function HS(e,t){return t==="|"?t:e}function $S(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:o,tableCell:u,tableRow:s}};function o(d,h,x,E){return a(c(d,x,E),d.align)}function s(d,h,x,E){const m=f(d,x,E),g=a([m]);return g.slice(0,g.indexOf(`
`))}function u(d,h,x,E){const m=x.enter("tableCell"),g=x.enter("phrasing"),y=x.containerPhrasing(d,{...E,before:l,after:l});return g(),m(),y}function a(d,h){return rS(d,{align:h,alignDelimiters:r,padding:n,stringLength:i})}function c(d,h,x){const E=d.children;let m=-1;const g=[],y=h.enter("table");for(;++m<E.length;)g[m]=f(E[m],h,x);return y(),g}function f(d,h,x){const E=d.children;let m=-1;const g=[],y=h.enter("tableRow");for(;++m<E.length;)g[m]=u(E[m],d,h,x);return y(),g}function p(d,h,x){let E=Sm.inlineCode(d,h,x);return x.stack.includes("tableCell")&&(E=E.replace(/\|/g,"\\$&")),E}}function VS(){return{exit:{taskListCheckValueChecked:Xf,taskListCheckValueUnchecked:Xf,paragraph:qS}}}function WS(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:QS}}}function Xf(e){const t=this.stack[this.stack.length-2];t.type,t.checked=e.type==="taskListCheckValueChecked"}function qS(e){const t=this.stack[this.stack.length-2];if(t&&t.type==="listItem"&&typeof t.checked=="boolean"){const n=this.stack[this.stack.length-1];n.type;const r=n.children[0];if(r&&r.type==="text"){const i=t.children;let l=-1,o;for(;++l<i.length;){const s=i[l];if(s.type==="paragraph"){o=s;break}}o===n&&(r.value=r.value.slice(1),r.value.length===0?n.children.shift():n.position&&r.position&&typeof r.position.start.offset=="number"&&(r.position.start.column++,r.position.start.offset++,n.position.start=Object.assign({},r.position.start)))}}this.exit(e)}function QS(e,t,n,r){const i=e.children[0],l=typeof e.checked=="boolean"&&i&&i.type==="paragraph",o="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);l&&s.move(o);let u=Sm.listItem(e,t,n,{...r,...s.current()});return l&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,a)),u;function a(c){return c+o}}function GS(){return[Tv(),Qv(),Jv(),DS(),VS()]}function KS(e){return{extensions:[Rv(),Gv(e),Yv(),$S(e),WS()]}}const XS={tokenize:nC,partial:!0},Cm={tokenize:rC,partial:!0},Em={tokenize:iC,partial:!0},bm={tokenize:lC,partial:!0},JS={tokenize:oC,partial:!0},Tm={name:"wwwAutolink",tokenize:eC,previous:Pm},Rm={name:"protocolAutolink",tokenize:tC,previous:_m},Vt={name:"emailAutolink",tokenize:ZS,previous:Am},Lt={};function YS(){return{text:Lt}}let gn=48;for(;gn<123;)Lt[gn]=Vt,gn++,gn===58?gn=65:gn===91&&(gn=97);Lt[43]=Vt;Lt[45]=Vt;Lt[46]=Vt;Lt[95]=Vt;Lt[72]=[Vt,Rm];Lt[104]=[Vt,Rm];Lt[87]=[Vt,Tm];Lt[119]=[Vt,Tm];function ZS(e,t,n){const r=this;let i,l;return o;function o(f){return!mu(f)||!Am.call(r,r.previous)||Fa(r.events)?n(f):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),s(f))}function s(f){return mu(f)?(e.consume(f),s):f===64?(e.consume(f),u):n(f)}function u(f){return f===46?e.check(JS,c,a)(f):f===45||f===95||Ie(f)?(l=!0,e.consume(f),u):c(f)}function a(f){return e.consume(f),i=!0,u}function c(f){return l&&i&&Ne(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(f)):n(f)}}function eC(e,t,n){const r=this;return i;function i(o){return o!==87&&o!==119||!Pm.call(r,r.previous)||Fa(r.events)?n(o):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(XS,e.attempt(Cm,e.attempt(Em,l),n),n)(o))}function l(o){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(o)}}function tC(e,t,n){const r=this;let i="",l=!1;return o;function o(f){return(f===72||f===104)&&_m.call(r,r.previous)&&!Fa(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(f),e.consume(f),s):n(f)}function s(f){if(Ne(f)&&i.length<5)return i+=String.fromCodePoint(f),e.consume(f),s;if(f===58){const p=i.toLowerCase();if(p==="http"||p==="https")return e.consume(f),u}return n(f)}function u(f){return f===47?(e.consume(f),l?a:(l=!0,u)):n(f)}function a(f){return f===null||Fl(f)||re(f)||In(f)||uo(f)?n(f):e.attempt(Cm,e.attempt(Em,c),n)(f)}function c(f){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(f)}}function nC(e,t,n){let r=0;return i;function i(o){return(o===87||o===119)&&r<3?(r++,e.consume(o),i):o===46&&r===3?(e.consume(o),l):n(o)}function l(o){return o===null?n(o):t(o)}}function rC(e,t,n){let r,i,l;return o;function o(a){return a===46||a===95?e.check(bm,u,s)(a):a===null||re(a)||In(a)||a!==45&&uo(a)?u(a):(l=!0,e.consume(a),o)}function s(a){return a===95?r=!0:(i=r,r=void 0),e.consume(a),o}function u(a){return i||r||!l?n(a):t(a)}}function iC(e,t){let n=0,r=0;return i;function i(o){return o===40?(n++,e.consume(o),i):o===41&&r<n?l(o):o===33||o===34||o===38||o===39||o===41||o===42||o===44||o===46||o===58||o===59||o===60||o===63||o===93||o===95||o===126?e.check(bm,t,l)(o):o===null||re(o)||In(o)?t(o):(e.consume(o),i)}function l(o){return o===41&&r++,e.consume(o),i}}function lC(e,t,n){return r;function r(s){return s===33||s===34||s===39||s===41||s===42||s===44||s===46||s===58||s===59||s===63||s===95||s===126?(e.consume(s),r):s===38?(e.consume(s),l):s===93?(e.consume(s),i):s===60||s===null||re(s)||In(s)?t(s):n(s)}function i(s){return s===null||s===40||s===91||re(s)||In(s)?t(s):r(s)}function l(s){return Ne(s)?o(s):n(s)}function o(s){return s===59?(e.consume(s),r):Ne(s)?(e.consume(s),o):n(s)}}function oC(e,t,n){return r;function r(l){return e.consume(l),i}function i(l){return Ie(l)?n(l):t(l)}}function Pm(e){return e===null||e===40||e===42||e===95||e===91||e===93||e===126||re(e)}function _m(e){return!Ne(e)}function Am(e){return!(e===47||mu(e))}function mu(e){return e===43||e===45||e===46||e===95||Ie(e)}function Fa(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if((r.type==="labelLink"||r.type==="labelImage")&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}const sC={tokenize:mC,partial:!0};function uC(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:pC,continuation:{tokenize:dC},exit:hC}},text:{91:{name:"gfmFootnoteCall",tokenize:fC},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:aC,resolveTo:cC}}}}function aC(e,t,n){const r=this;let i=r.events.length;const l=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let o;for(;i--;){const u=r.events[i][1];if(u.type==="labelImage"){o=u;break}if(u.type==="gfmFootnoteCall"||u.type==="labelLink"||u.type==="label"||u.type==="image"||u.type==="link")break}return s;function s(u){if(!o||!o._balanced)return n(u);const a=kt(r.sliceSerialize({start:o.end,end:r.now()}));return a.codePointAt(0)!==94||!l.includes(a.slice(1))?n(u):(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),t(u))}}function cC(e,t){let n=e.length;for(;n--;)if(e[n][1].type==="labelImage"&&e[n][0]==="enter"){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},s=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...s),e}function fC(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l=0,o;return s;function s(f){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),u}function u(f){return f!==94?n(f):(e.enter("gfmFootnoteCallMarker"),e.consume(f),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",a)}function a(f){if(l>999||f===93&&!o||f===null||f===91||re(f))return n(f);if(f===93){e.exit("chunkString");const p=e.exit("gfmFootnoteCallString");return i.includes(kt(r.sliceSerialize(p)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(f)}return re(f)||(o=!0),l++,e.consume(f),f===92?c:a}function c(f){return f===91||f===92||f===93?(e.consume(f),l++,a):a(f)}}function pC(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,o=0,s;return u;function u(h){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(h),e.exit("gfmFootnoteDefinitionLabelMarker"),a}function a(h){return h===94?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(h),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(h)}function c(h){if(o>999||h===93&&!s||h===null||h===91||re(h))return n(h);if(h===93){e.exit("chunkString");const x=e.exit("gfmFootnoteDefinitionLabelString");return l=kt(r.sliceSerialize(x)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(h),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return re(h)||(s=!0),o++,e.consume(h),h===92?f:c}function f(h){return h===91||h===92||h===93?(e.consume(h),o++,c):c(h)}function p(h){return h===58?(e.enter("definitionMarker"),e.consume(h),e.exit("definitionMarker"),i.includes(l)||i.push(l),X(e,d,"gfmFootnoteDefinitionWhitespace")):n(h)}function d(h){return t(h)}}function dC(e,t,n){return e.check(Ei,t,e.attempt(sC,t,n))}function hC(e){e.exit("gfmFootnoteDefinition")}function mC(e,t,n){const r=this;return X(e,i,"gfmFootnoteDefinitionIndent",5);function i(l){const o=r.events[r.events.length-1];return o&&o[1].type==="gfmFootnoteDefinitionIndent"&&o[2].sliceSerialize(o[1],!0).length===4?t(l):n(l)}}function gC(e){let n=(e||{}).singleTilde;const r={name:"strikethrough",tokenize:l,resolveAll:i};return n==null&&(n=!0),{text:{126:r},insideSpan:{null:[r]},attentionMarkers:{null:[126]}};function i(o,s){let u=-1;for(;++u<o.length;)if(o[u][0]==="enter"&&o[u][1].type==="strikethroughSequenceTemporary"&&o[u][1]._close){let a=u;for(;a--;)if(o[a][0]==="exit"&&o[a][1].type==="strikethroughSequenceTemporary"&&o[a][1]._open&&o[u][1].end.offset-o[u][1].start.offset===o[a][1].end.offset-o[a][1].start.offset){o[u][1].type="strikethroughSequence",o[a][1].type="strikethroughSequence";const c={type:"strikethrough",start:Object.assign({},o[a][1].start),end:Object.assign({},o[u][1].end)},f={type:"strikethroughText",start:Object.assign({},o[a][1].end),end:Object.assign({},o[u][1].start)},p=[["enter",c,s],["enter",o[a][1],s],["exit",o[a][1],s],["enter",f,s]],d=s.parser.constructs.insideSpan.null;d&&et(p,p.length,0,ao(d,o.slice(a+1,u),s)),et(p,p.length,0,[["exit",f,s],["enter",o[u][1],s],["exit",o[u][1],s],["exit",c,s]]),et(o,a-1,u-a+3,p),u=a+p.length-2;break}}for(u=-1;++u<o.length;)o[u][1].type==="strikethroughSequenceTemporary"&&(o[u][1].type="data");return o}function l(o,s,u){const a=this.previous,c=this.events;let f=0;return p;function p(h){return a===126&&c[c.length-1][1].type!=="characterEscape"?u(h):(o.enter("strikethroughSequenceTemporary"),d(h))}function d(h){const x=mr(a);if(h===126)return f>1?u(h):(o.consume(h),f++,d);if(f<2&&!n)return u(h);const E=o.exit("strikethroughSequenceTemporary"),m=mr(h);return E._open=!m||m===2&&!!x,E._close=!x||x===2&&!!m,s(h)}}}class yC{constructor(){this.map=[]}add(t,n,r){xC(this,t,n,r)}consume(t){if(this.map.sort(function(l,o){return l[0]-o[0]}),this.map.length===0)return;let n=this.map.length;const r=[];for(;n>0;)n-=1,r.push(t.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),t.length=this.map[n][0];r.push(t.slice()),t.length=0;let i=r.pop();for(;i;){for(const l of i)t.push(l);i=r.pop()}this.map.length=0}}function xC(e,t,n,r){let i=0;if(!(n===0&&r.length===0)){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}function kC(e,t){let n=!1;const r=[];for(;t<e.length;){const i=e[t];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&r.push(e[t+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(e[t-1][1].type==="tableDelimiterMarker"){const l=r.length-1;r[l]=r[l]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);t+=1}return r}function wC(){return{flow:{null:{name:"table",tokenize:vC,resolveAll:SC}}}}function vC(e,t,n){const r=this;let i=0,l=0,o;return s;function s(b){let j=r.events.length-1;for(;j>-1;){const ee=r.events[j][1].type;if(ee==="lineEnding"||ee==="linePrefix")j--;else break}const F=j>-1?r.events[j][1].type:null,W=F==="tableHead"||F==="tableRow"?C:u;return W===C&&r.parser.lazy[r.now().line]?n(b):W(b)}function u(b){return e.enter("tableHead"),e.enter("tableRow"),a(b)}function a(b){return b===124||(o=!0,l+=1),c(b)}function c(b){return b===null?n(b):M(b)?l>1?(l=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(b),e.exit("lineEnding"),d):n(b):q(b)?X(e,c,"whitespace")(b):(l+=1,o&&(o=!1,i+=1),b===124?(e.enter("tableCellDivider"),e.consume(b),e.exit("tableCellDivider"),o=!0,c):(e.enter("data"),f(b)))}function f(b){return b===null||b===124||re(b)?(e.exit("data"),c(b)):(e.consume(b),b===92?p:f)}function p(b){return b===92||b===124?(e.consume(b),f):f(b)}function d(b){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(b):(e.enter("tableDelimiterRow"),o=!1,q(b)?X(e,h,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(b):h(b))}function h(b){return b===45||b===58?E(b):b===124?(o=!0,e.enter("tableCellDivider"),e.consume(b),e.exit("tableCellDivider"),x):T(b)}function x(b){return q(b)?X(e,E,"whitespace")(b):E(b)}function E(b){return b===58?(l+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(b),e.exit("tableDelimiterMarker"),m):b===45?(l+=1,m(b)):b===null||M(b)?v(b):T(b)}function m(b){return b===45?(e.enter("tableDelimiterFiller"),g(b)):T(b)}function g(b){return b===45?(e.consume(b),g):b===58?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(b),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(b))}function y(b){return q(b)?X(e,v,"whitespace")(b):v(b)}function v(b){return b===124?h(b):b===null||M(b)?!o||i!==l?T(b):(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(b)):T(b)}function T(b){return n(b)}function C(b){return e.enter("tableRow"),_(b)}function _(b){return b===124?(e.enter("tableCellDivider"),e.consume(b),e.exit("tableCellDivider"),_):b===null||M(b)?(e.exit("tableRow"),t(b)):q(b)?X(e,_,"whitespace")(b):(e.enter("data"),A(b))}function A(b){return b===null||b===124||re(b)?(e.exit("data"),_(b)):(e.consume(b),b===92?N:A)}function N(b){return b===92||b===124?(e.consume(b),A):A(b)}}function SC(e,t){let n=-1,r=!0,i=0,l=[0,0,0,0],o=[0,0,0,0],s=!1,u=0,a,c,f;const p=new yC;for(;++n<e.length;){const d=e[n],h=d[1];d[0]==="enter"?h.type==="tableHead"?(s=!1,u!==0&&(Jf(p,t,u,a,c),c=void 0,u=0),a={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(n,0,[["enter",a,t]])):h.type==="tableRow"||h.type==="tableDelimiterRow"?(r=!0,f=void 0,l=[0,0,0,0],o=[0,n+1,0,0],s&&(s=!1,c={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(n,0,[["enter",c,t]])),i=h.type==="tableDelimiterRow"?2:c?3:1):i&&(h.type==="data"||h.type==="tableDelimiterMarker"||h.type==="tableDelimiterFiller")?(r=!1,o[2]===0&&(l[1]!==0&&(o[0]=o[1],f=Ki(p,t,l,i,void 0,f),l=[0,0,0,0]),o[2]=n)):h.type==="tableCellDivider"&&(r?r=!1:(l[1]!==0&&(o[0]=o[1],f=Ki(p,t,l,i,void 0,f)),l=o,o=[l[1],n,0,0])):h.type==="tableHead"?(s=!0,u=n):h.type==="tableRow"||h.type==="tableDelimiterRow"?(u=n,l[1]!==0?(o[0]=o[1],f=Ki(p,t,l,i,n,f)):o[1]!==0&&(f=Ki(p,t,o,i,n,f)),i=0):i&&(h.type==="data"||h.type==="tableDelimiterMarker"||h.type==="tableDelimiterFiller")&&(o[3]=n)}for(u!==0&&Jf(p,t,u,a,c),p.consume(t.events),n=-1;++n<t.events.length;){const d=t.events[n];d[0]==="enter"&&d[1].type==="table"&&(d[1]._align=kC(t.events,n))}return e}function Ki(e,t,n,r,i,l){const o=r===1?"tableHeader":r===2?"tableDelimiter":"tableData",s="tableContent";n[0]!==0&&(l.end=Object.assign({},Hn(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));const u=Hn(t.events,n[1]);if(l={type:o,start:Object.assign({},u),end:Object.assign({},u)},e.add(n[1],0,[["enter",l,t]]),n[2]!==0){const a=Hn(t.events,n[2]),c=Hn(t.events,n[3]),f={type:s,start:Object.assign({},a),end:Object.assign({},c)};if(e.add(n[2],0,[["enter",f,t]]),r!==2){const p=t.events[n[2]],d=t.events[n[3]];if(p[1].end=Object.assign({},d[1].end),p[1].type="chunkText",p[1].contentType="text",n[3]>n[2]+1){const h=n[2]+1,x=n[3]-n[2]-1;e.add(h,x,[])}}e.add(n[3]+1,0,[["exit",f,t]])}return i!==void 0&&(l.end=Object.assign({},Hn(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function Jf(e,t,n,r,i){const l=[],o=Hn(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function Hn(e,t){const n=e[t],r=n[0]==="enter"?"start":"end";return n[1][r]}const CC={name:"tasklistCheck",tokenize:bC};function EC(){return{text:{91:CC}}}function bC(e,t,n){const r=this;return i;function i(u){return r.previous!==null||!r._gfmTasklistFirstContentOfListItem?n(u):(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(u),e.exit("taskListCheckMarker"),l)}function l(u){return re(u)?(e.enter("taskListCheckValueUnchecked"),e.consume(u),e.exit("taskListCheckValueUnchecked"),o):u===88||u===120?(e.enter("taskListCheckValueChecked"),e.consume(u),e.exit("taskListCheckValueChecked"),o):n(u)}function o(u){return u===93?(e.enter("taskListCheckMarker"),e.consume(u),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),s):n(u)}function s(u){return M(u)?t(u):q(u)?e.check({tokenize:TC},t,n)(u):n(u)}}function TC(e,t,n){return X(e,r,"whitespace");function r(i){return i===null?n(i):t(i)}}function RC(e){return Mh([YS(),uC(),gC(e),wC(),EC()])}const PC={};function Lm(e){const t=this,n=e||PC,r=t.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push(RC(n)),l.push(GS()),o.push(KS(n))}const _C=({prompt:e,onUpdate:t})=>{const[n,r]=K.useState(e.content),[i,l]=K.useState(!1),[o,s]=K.useState(!1),u=async()=>{if(n.trim()===e.content.trim()){l(!1);return}try{s(!0),await t(e.id,n.trim()),l(!1)}catch{alert("Failed to save prompt")}finally{s(!1)}},a=()=>{r(e.content),l(!1)};return w.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:"8px",padding:"20px",backgroundColor:"white",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",height:"fit-content"},children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[w.jsx("h3",{style:{margin:0},children:e.name}),w.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["Version: ",e.version]})]}),i?w.jsxs("div",{children:[w.jsx("textarea",{value:n,onChange:c=>r(c.target.value),style:{width:"100%",minHeight:"150px",padding:"12px",border:"1px solid #ccc",borderRadius:"4px",fontSize:"14px",fontFamily:"monospace",resize:"vertical",lineHeight:"1.5"},placeholder:"Enter your prompt here..."}),w.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"8px",justifyContent:"flex-end"},children:[w.jsx("button",{onClick:a,disabled:o,style:{padding:"6px 12px",border:"1px solid #ccc",borderRadius:"4px",backgroundColor:"white",cursor:"pointer"},children:"Cancel"}),w.jsx("button",{onClick:u,disabled:o,style:{padding:"6px 12px",border:"none",borderRadius:"4px",backgroundColor:"#007bff",color:"white",cursor:"pointer"},children:o?"Saving...":"Save"})]})]}):w.jsxs("div",{children:[w.jsx("div",{style:{padding:"12px",backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"4px",minHeight:"150px",fontSize:"14px",fontFamily:"monospace",whiteSpace:"pre-wrap",overflowX:"auto",overflowWrap:"break-word",lineHeight:"1.5"},children:w.jsx(sm,{remarkPlugins:[Lm],children:e.content})}),w.jsx("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:"8px"},children:w.jsx("button",{onClick:()=>l(!0),style:{padding:"6px 12px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer"},children:"Edit"})})]})]})},AC=({value:e,onChange:t})=>{const[n,r]=K.useState(e||[{name:"",gap:""}]),i=(s,u,a)=>{const c=[...n];c[s][u]=a,r(c);const f=c.filter(p=>p.name.trim()&&p.gap.trim()).map((p,d)=>({user_id:d+1,competency_name:p.name.trim(),gap_percentage:parseFloat(p.gap.trim())||0}));t(f)},l=()=>{r([...n,{name:"",gap:""}])},o=s=>{if(n.length>1){const u=n.filter((c,f)=>f!==s);r(u);const a=u.filter(c=>c.name.trim()&&c.gap.trim()).map((c,f)=>({user_id:f+1,competency_name:c.name.trim(),gap_percentage:parseFloat(c.gap.trim())||0}));t(a)}};return w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold"},children:"Competency Gaps:"}),w.jsxs("div",{style:{marginBottom:"8px",fontSize:"12px",color:"#666"},children:[w.jsx("strong",{children:"competency name:"})," gap percent"]}),n.map((s,u)=>w.jsxs("div",{style:{display:"flex",gap:"8px",marginBottom:"8px",alignItems:"center"},children:[w.jsx("input",{type:"text",placeholder:"Competency name",value:s.name,onChange:a=>i(u,"name",a.target.value),style:{flex:2,padding:"6px",border:"1px solid #ccc",borderRadius:"4px",fontSize:"14px"}}),w.jsx("input",{type:"number",placeholder:"0-100",min:"0",max:"100",step:"0.01",value:s.gap,onChange:a=>i(u,"gap",a.target.value),style:{flex:1,padding:"6px",border:"1px solid #ccc",borderRadius:"4px",fontSize:"14px"}}),n.length>1&&w.jsx("button",{type:"button",onClick:()=>o(u),style:{padding:"6px 8px",border:"1px solid #dc3545",borderRadius:"4px",backgroundColor:"#dc3545",color:"white",cursor:"pointer",fontSize:"12px"},children:"×"})]},u)),w.jsx("button",{type:"button",onClick:l,style:{padding:"6px 12px",border:"1px solid #28a745",borderRadius:"4px",backgroundColor:"#28a745",color:"white",cursor:"pointer",fontSize:"14px",marginTop:"4px"},children:"+ Add new row"})]})},LC=({onRun:e})=>{const[t,n]=K.useState(""),[r,i]=K.useState([]),[l,o]=K.useState(!1),[s,u]=K.useState(null),a=async()=>{if(!t.trim()){alert("Please enter a role name");return}if(!r||r.length===0){alert("Please enter at least one competency gap");return}try{o(!0);const c=await e({roleName:t.trim(),competencyGaps:r});u(c)}catch{alert("Failed to run evaluation")}finally{o(!1)}};return w.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:"8px",padding:"16px",backgroundColor:"white"},children:[w.jsx("h3",{style:{marginTop:0},children:"Run Evaluation"}),w.jsxs("div",{style:{marginBottom:"16px"},children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold"},children:"Role Name:"}),w.jsx("input",{type:"text",placeholder:"e.g., Project Manager, Software Developer, etc.",value:t,onChange:c=>n(c.target.value),style:{width:"100%",padding:"8px",border:"1px solid #ccc",borderRadius:"4px",fontSize:"14px"}})]}),w.jsx("div",{style:{marginBottom:"16px"},children:w.jsx(AC,{value:r,onChange:i})}),w.jsx("button",{onClick:a,disabled:l||!t.trim()||!r||r.length===0,style:{padding:"10px 20px",border:"none",borderRadius:"4px",backgroundColor:l?"#6c757d":"#28a745",color:"white",cursor:l?"not-allowed":"pointer",fontSize:"16px",fontWeight:"bold"},children:l?"Running...":"Run Prompt Chain"}),s&&w.jsxs("div",{style:{marginTop:"16px",padding:"12px",backgroundColor:"#d4edda",border:"1px solid #c3e6cb",borderRadius:"4px"},children:[w.jsx("h4",{style:{margin:"0 0 8px 0",color:"#155724"},children:"Latest Result:"}),w.jsx("div",{style:{fontSize:"14px",fontFamily:"monospace",whiteSpace:"pre-wrap",maxHeight:"200px",overflow:"auto"},children:s.output}),w.jsxs("div",{style:{fontSize:"12px",color:"#666",marginTop:"8px"},children:["Completed at: ",new Date(s.timestamp).toLocaleString()]})]})]})};function Im(e,t){return function(){return e.apply(t,arguments)}}const{toString:IC}=Object.prototype,{getPrototypeOf:Ma}=Object,{iterator:ho,toStringTag:zm}=Symbol,mo=(e=>t=>{const n=IC.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),vt=e=>(e=e.toLowerCase(),t=>mo(t)===e),go=e=>t=>typeof t===e,{isArray:vr}=Array,yi=go("undefined");function zC(e){return e!==null&&!yi(e)&&e.constructor!==null&&!yi(e.constructor)&&qe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Om=vt("ArrayBuffer");function OC(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Om(e.buffer),t}const jC=go("string"),qe=go("function"),jm=go("number"),yo=e=>e!==null&&typeof e=="object",NC=e=>e===!0||e===!1,cl=e=>{if(mo(e)!=="object")return!1;const t=Ma(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(zm in e)&&!(ho in e)},DC=vt("Date"),FC=vt("File"),MC=vt("Blob"),BC=vt("FileList"),UC=e=>yo(e)&&qe(e.pipe),HC=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||qe(e.append)&&((t=mo(e))==="formdata"||t==="object"&&qe(e.toString)&&e.toString()==="[object FormData]"))},$C=vt("URLSearchParams"),[VC,WC,qC,QC]=["ReadableStream","Request","Response","Headers"].map(vt),GC=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ti(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),vr(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const l=n?Object.getOwnPropertyNames(e):Object.keys(e),o=l.length;let s;for(r=0;r<o;r++)s=l[r],t.call(null,e[s],s,e)}}function Nm(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Cn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Dm=e=>!yi(e)&&e!==Cn;function gu(){const{caseless:e}=Dm(this)&&this||{},t={},n=(r,i)=>{const l=e&&Nm(t,i)||i;cl(t[l])&&cl(r)?t[l]=gu(t[l],r):cl(r)?t[l]=gu({},r):vr(r)?t[l]=r.slice():t[l]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&Ti(arguments[r],n);return t}const KC=(e,t,n,{allOwnKeys:r}={})=>(Ti(t,(i,l)=>{n&&qe(i)?e[l]=Im(i,n):e[l]=i},{allOwnKeys:r}),e),XC=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),JC=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},YC=(e,t,n,r)=>{let i,l,o;const s={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),l=i.length;l-- >0;)o=i[l],(!r||r(o,e,t))&&!s[o]&&(t[o]=e[o],s[o]=!0);e=n!==!1&&Ma(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},ZC=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},eE=e=>{if(!e)return null;if(vr(e))return e;let t=e.length;if(!jm(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},tE=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ma(Uint8Array)),nE=(e,t)=>{const r=(e&&e[ho]).call(e);let i;for(;(i=r.next())&&!i.done;){const l=i.value;t.call(e,l[0],l[1])}},rE=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},iE=vt("HTMLFormElement"),lE=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),Yf=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),oE=vt("RegExp"),Fm=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Ti(n,(i,l)=>{let o;(o=t(i,l,e))!==!1&&(r[l]=o||i)}),Object.defineProperties(e,r)},sE=e=>{Fm(e,(t,n)=>{if(qe(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(qe(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},uE=(e,t)=>{const n={},r=i=>{i.forEach(l=>{n[l]=!0})};return vr(e)?r(e):r(String(e).split(t)),n},aE=()=>{},cE=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function fE(e){return!!(e&&qe(e.append)&&e[zm]==="FormData"&&e[ho])}const pE=e=>{const t=new Array(10),n=(r,i)=>{if(yo(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const l=vr(r)?[]:{};return Ti(r,(o,s)=>{const u=n(o,i+1);!yi(u)&&(l[s]=u)}),t[i]=void 0,l}}return r};return n(e,0)},dE=vt("AsyncFunction"),hE=e=>e&&(yo(e)||qe(e))&&qe(e.then)&&qe(e.catch),Mm=((e,t)=>e?setImmediate:t?((n,r)=>(Cn.addEventListener("message",({source:i,data:l})=>{i===Cn&&l===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Cn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",qe(Cn.postMessage)),mE=typeof queueMicrotask<"u"?queueMicrotask.bind(Cn):typeof process<"u"&&process.nextTick||Mm,gE=e=>e!=null&&qe(e[ho]),R={isArray:vr,isArrayBuffer:Om,isBuffer:zC,isFormData:HC,isArrayBufferView:OC,isString:jC,isNumber:jm,isBoolean:NC,isObject:yo,isPlainObject:cl,isReadableStream:VC,isRequest:WC,isResponse:qC,isHeaders:QC,isUndefined:yi,isDate:DC,isFile:FC,isBlob:MC,isRegExp:oE,isFunction:qe,isStream:UC,isURLSearchParams:$C,isTypedArray:tE,isFileList:BC,forEach:Ti,merge:gu,extend:KC,trim:GC,stripBOM:XC,inherits:JC,toFlatObject:YC,kindOf:mo,kindOfTest:vt,endsWith:ZC,toArray:eE,forEachEntry:nE,matchAll:rE,isHTMLForm:iE,hasOwnProperty:Yf,hasOwnProp:Yf,reduceDescriptors:Fm,freezeMethods:sE,toObjectSet:uE,toCamelCase:lE,noop:aE,toFiniteNumber:cE,findKey:Nm,global:Cn,isContextDefined:Dm,isSpecCompliantForm:fE,toJSONObject:pE,isAsyncFn:dE,isThenable:hE,setImmediate:Mm,asap:mE,isIterable:gE};function H(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}R.inherits(H,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:R.toJSONObject(this.config),code:this.code,status:this.status}}});const Bm=H.prototype,Um={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Um[e]={value:e}});Object.defineProperties(H,Um);Object.defineProperty(Bm,"isAxiosError",{value:!0});H.from=(e,t,n,r,i,l)=>{const o=Object.create(Bm);return R.toFlatObject(e,o,function(u){return u!==Error.prototype},s=>s!=="isAxiosError"),H.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,l&&Object.assign(o,l),o};const yE=null;function yu(e){return R.isPlainObject(e)||R.isArray(e)}function Hm(e){return R.endsWith(e,"[]")?e.slice(0,-2):e}function Zf(e,t,n){return e?e.concat(t).map(function(i,l){return i=Hm(i),!n&&l?"["+i+"]":i}).join(n?".":""):t}function xE(e){return R.isArray(e)&&!e.some(yu)}const kE=R.toFlatObject(R,{},null,function(t){return/^is[A-Z]/.test(t)});function xo(e,t,n){if(!R.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=R.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,E){return!R.isUndefined(E[x])});const r=n.metaTokens,i=n.visitor||c,l=n.dots,o=n.indexes,u=(n.Blob||typeof Blob<"u"&&Blob)&&R.isSpecCompliantForm(t);if(!R.isFunction(i))throw new TypeError("visitor must be a function");function a(h){if(h===null)return"";if(R.isDate(h))return h.toISOString();if(!u&&R.isBlob(h))throw new H("Blob is not supported. Use a Buffer instead.");return R.isArrayBuffer(h)||R.isTypedArray(h)?u&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function c(h,x,E){let m=h;if(h&&!E&&typeof h=="object"){if(R.endsWith(x,"{}"))x=r?x:x.slice(0,-2),h=JSON.stringify(h);else if(R.isArray(h)&&xE(h)||(R.isFileList(h)||R.endsWith(x,"[]"))&&(m=R.toArray(h)))return x=Hm(x),m.forEach(function(y,v){!(R.isUndefined(y)||y===null)&&t.append(o===!0?Zf([x],v,l):o===null?x:x+"[]",a(y))}),!1}return yu(h)?!0:(t.append(Zf(E,x,l),a(h)),!1)}const f=[],p=Object.assign(kE,{defaultVisitor:c,convertValue:a,isVisitable:yu});function d(h,x){if(!R.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+x.join("."));f.push(h),R.forEach(h,function(m,g){(!(R.isUndefined(m)||m===null)&&i.call(t,m,R.isString(g)?g.trim():g,x,p))===!0&&d(m,x?x.concat(g):[g])}),f.pop()}}if(!R.isObject(e))throw new TypeError("data must be an object");return d(e),t}function ep(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ba(e,t){this._pairs=[],e&&xo(e,this,t)}const $m=Ba.prototype;$m.append=function(t,n){this._pairs.push([t,n])};$m.toString=function(t){const n=t?function(r){return t.call(this,r,ep)}:ep;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function wE(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Vm(e,t,n){if(!t)return e;const r=n&&n.encode||wE;R.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let l;if(i?l=i(t,n):l=R.isURLSearchParams(t)?t.toString():new Ba(t,n).toString(r),l){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+l}return e}class tp{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){R.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Wm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},vE=typeof URLSearchParams<"u"?URLSearchParams:Ba,SE=typeof FormData<"u"?FormData:null,CE=typeof Blob<"u"?Blob:null,EE={isBrowser:!0,classes:{URLSearchParams:vE,FormData:SE,Blob:CE},protocols:["http","https","file","blob","url","data"]},Ua=typeof window<"u"&&typeof document<"u",xu=typeof navigator=="object"&&navigator||void 0,bE=Ua&&(!xu||["ReactNative","NativeScript","NS"].indexOf(xu.product)<0),TE=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",RE=Ua&&window.location.href||"http://localhost",PE=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ua,hasStandardBrowserEnv:bE,hasStandardBrowserWebWorkerEnv:TE,navigator:xu,origin:RE},Symbol.toStringTag,{value:"Module"})),Le={...PE,...EE};function _E(e,t){return xo(e,new Le.classes.URLSearchParams,Object.assign({visitor:function(n,r,i,l){return Le.isNode&&R.isBuffer(n)?(this.append(r,n.toString("base64")),!1):l.defaultVisitor.apply(this,arguments)}},t))}function AE(e){return R.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function LE(e){const t={},n=Object.keys(e);let r;const i=n.length;let l;for(r=0;r<i;r++)l=n[r],t[l]=e[l];return t}function qm(e){function t(n,r,i,l){let o=n[l++];if(o==="__proto__")return!0;const s=Number.isFinite(+o),u=l>=n.length;return o=!o&&R.isArray(i)?i.length:o,u?(R.hasOwnProp(i,o)?i[o]=[i[o],r]:i[o]=r,!s):((!i[o]||!R.isObject(i[o]))&&(i[o]=[]),t(n,r,i[o],l)&&R.isArray(i[o])&&(i[o]=LE(i[o])),!s)}if(R.isFormData(e)&&R.isFunction(e.entries)){const n={};return R.forEachEntry(e,(r,i)=>{t(AE(r),i,n,0)}),n}return null}function IE(e,t,n){if(R.isString(e))try{return(t||JSON.parse)(e),R.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Ri={transitional:Wm,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,l=R.isObject(t);if(l&&R.isHTMLForm(t)&&(t=new FormData(t)),R.isFormData(t))return i?JSON.stringify(qm(t)):t;if(R.isArrayBuffer(t)||R.isBuffer(t)||R.isStream(t)||R.isFile(t)||R.isBlob(t)||R.isReadableStream(t))return t;if(R.isArrayBufferView(t))return t.buffer;if(R.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let s;if(l){if(r.indexOf("application/x-www-form-urlencoded")>-1)return _E(t,this.formSerializer).toString();if((s=R.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return xo(s?{"files[]":t}:t,u&&new u,this.formSerializer)}}return l||i?(n.setContentType("application/json",!1),IE(t)):t}],transformResponse:[function(t){const n=this.transitional||Ri.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(R.isResponse(t)||R.isReadableStream(t))return t;if(t&&R.isString(t)&&(r&&!this.responseType||i)){const o=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(s){if(o)throw s.name==="SyntaxError"?H.from(s,H.ERR_BAD_RESPONSE,this,null,this.response):s}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Le.classes.FormData,Blob:Le.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};R.forEach(["delete","get","head","post","put","patch"],e=>{Ri.headers[e]={}});const zE=R.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),OE=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),n=o.substring(0,i).trim().toLowerCase(),r=o.substring(i+1).trim(),!(!n||t[n]&&zE[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},np=Symbol("internals");function jr(e){return e&&String(e).trim().toLowerCase()}function fl(e){return e===!1||e==null?e:R.isArray(e)?e.map(fl):String(e)}function jE(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const NE=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function us(e,t,n,r,i){if(R.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!R.isString(t)){if(R.isString(r))return t.indexOf(r)!==-1;if(R.isRegExp(r))return r.test(t)}}function DE(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function FE(e,t){const n=R.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,l,o){return this[r].call(this,t,i,l,o)},configurable:!0})})}let Qe=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function l(s,u,a){const c=jr(u);if(!c)throw new Error("header name must be a non-empty string");const f=R.findKey(i,c);(!f||i[f]===void 0||a===!0||a===void 0&&i[f]!==!1)&&(i[f||u]=fl(s))}const o=(s,u)=>R.forEach(s,(a,c)=>l(a,c,u));if(R.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(R.isString(t)&&(t=t.trim())&&!NE(t))o(OE(t),n);else if(R.isObject(t)&&R.isIterable(t)){let s={},u,a;for(const c of t){if(!R.isArray(c))throw TypeError("Object iterator must return a key-value pair");s[a=c[0]]=(u=s[a])?R.isArray(u)?[...u,c[1]]:[u,c[1]]:c[1]}o(s,n)}else t!=null&&l(n,t,r);return this}get(t,n){if(t=jr(t),t){const r=R.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return jE(i);if(R.isFunction(n))return n.call(this,i,r);if(R.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=jr(t),t){const r=R.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||us(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function l(o){if(o=jr(o),o){const s=R.findKey(r,o);s&&(!n||us(r,r[s],s,n))&&(delete r[s],i=!0)}}return R.isArray(t)?t.forEach(l):l(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const l=n[r];(!t||us(this,this[l],l,t,!0))&&(delete this[l],i=!0)}return i}normalize(t){const n=this,r={};return R.forEach(this,(i,l)=>{const o=R.findKey(r,l);if(o){n[o]=fl(i),delete n[l];return}const s=t?DE(l):String(l).trim();s!==l&&delete n[l],n[s]=fl(i),r[s]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return R.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&R.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[np]=this[np]={accessors:{}}).accessors,i=this.prototype;function l(o){const s=jr(o);r[s]||(FE(i,o),r[s]=!0)}return R.isArray(t)?t.forEach(l):l(t),this}};Qe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);R.reduceDescriptors(Qe.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});R.freezeMethods(Qe);function as(e,t){const n=this||Ri,r=t||n,i=Qe.from(r.headers);let l=r.data;return R.forEach(e,function(s){l=s.call(n,l,i.normalize(),t?t.status:void 0)}),i.normalize(),l}function Qm(e){return!!(e&&e.__CANCEL__)}function Sr(e,t,n){H.call(this,e??"canceled",H.ERR_CANCELED,t,n),this.name="CanceledError"}R.inherits(Sr,H,{__CANCEL__:!0});function Gm(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new H("Request failed with status code "+n.status,[H.ERR_BAD_REQUEST,H.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function ME(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function BE(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,l=0,o;return t=t!==void 0?t:1e3,function(u){const a=Date.now(),c=r[l];o||(o=a),n[i]=u,r[i]=a;let f=l,p=0;for(;f!==i;)p+=n[f++],f=f%e;if(i=(i+1)%e,i===l&&(l=(l+1)%e),a-o<t)return;const d=c&&a-c;return d?Math.round(p*1e3/d):void 0}}function UE(e,t){let n=0,r=1e3/t,i,l;const o=(a,c=Date.now())=>{n=c,i=null,l&&(clearTimeout(l),l=null),e.apply(null,a)};return[(...a)=>{const c=Date.now(),f=c-n;f>=r?o(a,c):(i=a,l||(l=setTimeout(()=>{l=null,o(i)},r-f)))},()=>i&&o(i)]}const Hl=(e,t,n=3)=>{let r=0;const i=BE(50,250);return UE(l=>{const o=l.loaded,s=l.lengthComputable?l.total:void 0,u=o-r,a=i(u),c=o<=s;r=o;const f={loaded:o,total:s,progress:s?o/s:void 0,bytes:u,rate:a||void 0,estimated:a&&s&&c?(s-o)/a:void 0,event:l,lengthComputable:s!=null,[t?"download":"upload"]:!0};e(f)},n)},rp=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},ip=e=>(...t)=>R.asap(()=>e(...t)),HE=Le.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Le.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Le.origin),Le.navigator&&/(msie|trident)/i.test(Le.navigator.userAgent)):()=>!0,$E=Le.hasStandardBrowserEnv?{write(e,t,n,r,i,l){const o=[e+"="+encodeURIComponent(t)];R.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),R.isString(r)&&o.push("path="+r),R.isString(i)&&o.push("domain="+i),l===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function VE(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function WE(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Km(e,t,n){let r=!VE(t);return e&&(r||n==!1)?WE(e,t):t}const lp=e=>e instanceof Qe?{...e}:e;function zn(e,t){t=t||{};const n={};function r(a,c,f,p){return R.isPlainObject(a)&&R.isPlainObject(c)?R.merge.call({caseless:p},a,c):R.isPlainObject(c)?R.merge({},c):R.isArray(c)?c.slice():c}function i(a,c,f,p){if(R.isUndefined(c)){if(!R.isUndefined(a))return r(void 0,a,f,p)}else return r(a,c,f,p)}function l(a,c){if(!R.isUndefined(c))return r(void 0,c)}function o(a,c){if(R.isUndefined(c)){if(!R.isUndefined(a))return r(void 0,a)}else return r(void 0,c)}function s(a,c,f){if(f in t)return r(a,c);if(f in e)return r(void 0,a)}const u={url:l,method:l,data:l,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(a,c,f)=>i(lp(a),lp(c),f,!0)};return R.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=u[c]||i,p=f(e[c],t[c],c);R.isUndefined(p)&&f!==s||(n[c]=p)}),n}const Xm=e=>{const t=zn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:l,headers:o,auth:s}=t;t.headers=o=Qe.from(o),t.url=Vm(Km(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&o.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):"")));let u;if(R.isFormData(n)){if(Le.hasStandardBrowserEnv||Le.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((u=o.getContentType())!==!1){const[a,...c]=u?u.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([a||"multipart/form-data",...c].join("; "))}}if(Le.hasStandardBrowserEnv&&(r&&R.isFunction(r)&&(r=r(t)),r||r!==!1&&HE(t.url))){const a=i&&l&&$E.read(l);a&&o.set(i,a)}return t},qE=typeof XMLHttpRequest<"u",QE=qE&&function(e){return new Promise(function(n,r){const i=Xm(e);let l=i.data;const o=Qe.from(i.headers).normalize();let{responseType:s,onUploadProgress:u,onDownloadProgress:a}=i,c,f,p,d,h;function x(){d&&d(),h&&h(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let E=new XMLHttpRequest;E.open(i.method.toUpperCase(),i.url,!0),E.timeout=i.timeout;function m(){if(!E)return;const y=Qe.from("getAllResponseHeaders"in E&&E.getAllResponseHeaders()),T={data:!s||s==="text"||s==="json"?E.responseText:E.response,status:E.status,statusText:E.statusText,headers:y,config:e,request:E};Gm(function(_){n(_),x()},function(_){r(_),x()},T),E=null}"onloadend"in E?E.onloadend=m:E.onreadystatechange=function(){!E||E.readyState!==4||E.status===0&&!(E.responseURL&&E.responseURL.indexOf("file:")===0)||setTimeout(m)},E.onabort=function(){E&&(r(new H("Request aborted",H.ECONNABORTED,e,E)),E=null)},E.onerror=function(){r(new H("Network Error",H.ERR_NETWORK,e,E)),E=null},E.ontimeout=function(){let v=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const T=i.transitional||Wm;i.timeoutErrorMessage&&(v=i.timeoutErrorMessage),r(new H(v,T.clarifyTimeoutError?H.ETIMEDOUT:H.ECONNABORTED,e,E)),E=null},l===void 0&&o.setContentType(null),"setRequestHeader"in E&&R.forEach(o.toJSON(),function(v,T){E.setRequestHeader(T,v)}),R.isUndefined(i.withCredentials)||(E.withCredentials=!!i.withCredentials),s&&s!=="json"&&(E.responseType=i.responseType),a&&([p,h]=Hl(a,!0),E.addEventListener("progress",p)),u&&E.upload&&([f,d]=Hl(u),E.upload.addEventListener("progress",f),E.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(c=y=>{E&&(r(!y||y.type?new Sr(null,e,E):y),E.abort(),E=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));const g=ME(i.url);if(g&&Le.protocols.indexOf(g)===-1){r(new H("Unsupported protocol "+g+":",H.ERR_BAD_REQUEST,e));return}E.send(l||null)})},GE=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const l=function(a){if(!i){i=!0,s();const c=a instanceof Error?a:this.reason;r.abort(c instanceof H?c:new Sr(c instanceof Error?c.message:c))}};let o=t&&setTimeout(()=>{o=null,l(new H(`timeout ${t} of ms exceeded`,H.ETIMEDOUT))},t);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(a=>{a.unsubscribe?a.unsubscribe(l):a.removeEventListener("abort",l)}),e=null)};e.forEach(a=>a.addEventListener("abort",l));const{signal:u}=r;return u.unsubscribe=()=>R.asap(s),u}},KE=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},XE=async function*(e,t){for await(const n of JE(e))yield*KE(n,t)},JE=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},op=(e,t,n,r)=>{const i=XE(e,t);let l=0,o,s=u=>{o||(o=!0,r&&r(u))};return new ReadableStream({async pull(u){try{const{done:a,value:c}=await i.next();if(a){s(),u.close();return}let f=c.byteLength;if(n){let p=l+=f;n(p)}u.enqueue(new Uint8Array(c))}catch(a){throw s(a),a}},cancel(u){return s(u),i.return()}},{highWaterMark:2})},ko=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Jm=ko&&typeof ReadableStream=="function",YE=ko&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ym=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ZE=Jm&&Ym(()=>{let e=!1;const t=new Request(Le.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),sp=64*1024,ku=Jm&&Ym(()=>R.isReadableStream(new Response("").body)),$l={stream:ku&&(e=>e.body)};ko&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!$l[t]&&($l[t]=R.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new H(`Response type '${t}' is not supported`,H.ERR_NOT_SUPPORT,r)})})})(new Response);const e2=async e=>{if(e==null)return 0;if(R.isBlob(e))return e.size;if(R.isSpecCompliantForm(e))return(await new Request(Le.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(R.isArrayBufferView(e)||R.isArrayBuffer(e))return e.byteLength;if(R.isURLSearchParams(e)&&(e=e+""),R.isString(e))return(await YE(e)).byteLength},t2=async(e,t)=>{const n=R.toFiniteNumber(e.getContentLength());return n??e2(t)},n2=ko&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:l,timeout:o,onDownloadProgress:s,onUploadProgress:u,responseType:a,headers:c,withCredentials:f="same-origin",fetchOptions:p}=Xm(e);a=a?(a+"").toLowerCase():"text";let d=GE([i,l&&l.toAbortSignal()],o),h;const x=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let E;try{if(u&&ZE&&n!=="get"&&n!=="head"&&(E=await t2(c,r))!==0){let T=new Request(t,{method:"POST",body:r,duplex:"half"}),C;if(R.isFormData(r)&&(C=T.headers.get("content-type"))&&c.setContentType(C),T.body){const[_,A]=rp(E,Hl(ip(u)));r=op(T.body,sp,_,A)}}R.isString(f)||(f=f?"include":"omit");const m="credentials"in Request.prototype;h=new Request(t,{...p,signal:d,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:m?f:void 0});let g=await fetch(h);const y=ku&&(a==="stream"||a==="response");if(ku&&(s||y&&x)){const T={};["status","statusText","headers"].forEach(N=>{T[N]=g[N]});const C=R.toFiniteNumber(g.headers.get("content-length")),[_,A]=s&&rp(C,Hl(ip(s),!0))||[];g=new Response(op(g.body,sp,_,()=>{A&&A(),x&&x()}),T)}a=a||"text";let v=await $l[R.findKey($l,a)||"text"](g,e);return!y&&x&&x(),await new Promise((T,C)=>{Gm(T,C,{data:v,headers:Qe.from(g.headers),status:g.status,statusText:g.statusText,config:e,request:h})})}catch(m){throw x&&x(),m&&m.name==="TypeError"&&/Load failed|fetch/i.test(m.message)?Object.assign(new H("Network Error",H.ERR_NETWORK,e,h),{cause:m.cause||m}):H.from(m,m&&m.code,e,h)}}),wu={http:yE,xhr:QE,fetch:n2};R.forEach(wu,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const up=e=>`- ${e}`,r2=e=>R.isFunction(e)||e===null||e===!1,Zm={getAdapter:e=>{e=R.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let l=0;l<t;l++){n=e[l];let o;if(r=n,!r2(n)&&(r=wu[(o=String(n)).toLowerCase()],r===void 0))throw new H(`Unknown adapter '${o}'`);if(r)break;i[o||"#"+l]=r}if(!r){const l=Object.entries(i).map(([s,u])=>`adapter ${s} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=t?l.length>1?`since :
`+l.map(up).join(`
`):" "+up(l[0]):"as no adapter specified";throw new H("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:wu};function cs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Sr(null,e)}function ap(e){return cs(e),e.headers=Qe.from(e.headers),e.data=as.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Zm.getAdapter(e.adapter||Ri.adapter)(e).then(function(r){return cs(e),r.data=as.call(e,e.transformResponse,r),r.headers=Qe.from(r.headers),r},function(r){return Qm(r)||(cs(e),r&&r.response&&(r.response.data=as.call(e,e.transformResponse,r.response),r.response.headers=Qe.from(r.response.headers))),Promise.reject(r)})}const eg="1.9.0",wo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{wo[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const cp={};wo.transitional=function(t,n,r){function i(l,o){return"[Axios v"+eg+"] Transitional option '"+l+"'"+o+(r?". "+r:"")}return(l,o,s)=>{if(t===!1)throw new H(i(o," has been removed"+(n?" in "+n:"")),H.ERR_DEPRECATED);return n&&!cp[o]&&(cp[o]=!0,console.warn(i(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(l,o,s):!0}};wo.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function i2(e,t,n){if(typeof e!="object")throw new H("options must be an object",H.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const l=r[i],o=t[l];if(o){const s=e[l],u=s===void 0||o(s,l,e);if(u!==!0)throw new H("option "+l+" must be "+u,H.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new H("Unknown option "+l,H.ERR_BAD_OPTION)}}const pl={assertOptions:i2,validators:wo},Et=pl.validators;let Tn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new tp,response:new tp}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const l=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?l&&!String(r.stack).endsWith(l.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+l):r.stack=l}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=zn(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:l}=n;r!==void 0&&pl.assertOptions(r,{silentJSONParsing:Et.transitional(Et.boolean),forcedJSONParsing:Et.transitional(Et.boolean),clarifyTimeoutError:Et.transitional(Et.boolean)},!1),i!=null&&(R.isFunction(i)?n.paramsSerializer={serialize:i}:pl.assertOptions(i,{encode:Et.function,serialize:Et.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),pl.assertOptions(n,{baseUrl:Et.spelling("baseURL"),withXsrfToken:Et.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=l&&R.merge(l.common,l[n.method]);l&&R.forEach(["delete","get","head","post","put","patch","common"],h=>{delete l[h]}),n.headers=Qe.concat(o,l);const s=[];let u=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(u=u&&x.synchronous,s.unshift(x.fulfilled,x.rejected))});const a=[];this.interceptors.response.forEach(function(x){a.push(x.fulfilled,x.rejected)});let c,f=0,p;if(!u){const h=[ap.bind(this),void 0];for(h.unshift.apply(h,s),h.push.apply(h,a),p=h.length,c=Promise.resolve(n);f<p;)c=c.then(h[f++],h[f++]);return c}p=s.length;let d=n;for(f=0;f<p;){const h=s[f++],x=s[f++];try{d=h(d)}catch(E){x.call(this,E);break}}try{c=ap.call(this,d)}catch(h){return Promise.reject(h)}for(f=0,p=a.length;f<p;)c=c.then(a[f++],a[f++]);return c}getUri(t){t=zn(this.defaults,t);const n=Km(t.baseURL,t.url,t.allowAbsoluteUrls);return Vm(n,t.params,t.paramsSerializer)}};R.forEach(["delete","get","head","options"],function(t){Tn.prototype[t]=function(n,r){return this.request(zn(r||{},{method:t,url:n,data:(r||{}).data}))}});R.forEach(["post","put","patch"],function(t){function n(r){return function(l,o,s){return this.request(zn(s||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:l,data:o}))}}Tn.prototype[t]=n(),Tn.prototype[t+"Form"]=n(!0)});let l2=class tg{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(l){n=l});const r=this;this.promise.then(i=>{if(!r._listeners)return;let l=r._listeners.length;for(;l-- >0;)r._listeners[l](i);r._listeners=null}),this.promise.then=i=>{let l;const o=new Promise(s=>{r.subscribe(s),l=s}).then(i);return o.cancel=function(){r.unsubscribe(l)},o},t(function(l,o,s){r.reason||(r.reason=new Sr(l,o,s),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new tg(function(i){t=i}),cancel:t}}};function o2(e){return function(n){return e.apply(null,n)}}function s2(e){return R.isObject(e)&&e.isAxiosError===!0}const vu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(vu).forEach(([e,t])=>{vu[t]=e});function ng(e){const t=new Tn(e),n=Im(Tn.prototype.request,t);return R.extend(n,Tn.prototype,t,{allOwnKeys:!0}),R.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return ng(zn(e,i))},n}const me=ng(Ri);me.Axios=Tn;me.CanceledError=Sr;me.CancelToken=l2;me.isCancel=Qm;me.VERSION=eg;me.toFormData=xo;me.AxiosError=H;me.Cancel=me.CanceledError;me.all=function(t){return Promise.all(t)};me.spread=o2;me.isAxiosError=s2;me.mergeConfig=zn;me.AxiosHeaders=Qe;me.formToJSON=e=>qm(R.isHTMLForm(e)?new FormData(e):e);me.getAdapter=Zm.getAdapter;me.HttpStatusCode=vu;me.default=me;const{Axios:x2,AxiosError:k2,CanceledError:w2,isCancel:v2,CancelToken:S2,VERSION:C2,all:E2,Cancel:b2,isAxiosError:T2,spread:R2,toFormData:P2,AxiosHeaders:_2,HttpStatusCode:A2,formToJSON:L2,getAdapter:I2,mergeConfig:z2}=me,rg="/api",Pt=me.create({baseURL:rg,headers:{"Content-Type":"application/json"}}),fp=me.create({baseURL:rg}),Vl={getAll:()=>Pt.get("/prompts"),getById:e=>Pt.get(`/prompts/${e}`),getVersion:(e,t)=>Pt.get(`/prompts/${e}/versions/${t}`),update:(e,t)=>Pt.put(`/prompts/${e}`,{content:t})},Su={getAll:()=>Pt.get("/evaluations"),run:e=>Pt.post("/evaluations/run",{input:e}),updateAnnotation:(e,t)=>Pt.put(`/evaluations/${e}/annotation`,{annotation:t})},Cu={getAll:()=>Pt.get("/lgd-evaluations"),run:e=>Pt.post("/lgd-evaluations/run",{input:e}),updateAnnotation:(e,t)=>Pt.put(`/lgd-evaluations/${e}/annotation`,{annotation:t})},pp={getSampleTranscript:()=>fp.get("/data/lgd_transcript.txt"),getSampleCompetencies:()=>fp.get("/data/lgd_competency_guidelines.txt")},u2=({isOpen:e,onClose:t,title:n,content:r})=>e?w.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e4},onClick:t,children:w.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",padding:"20px",maxWidth:"80%",maxHeight:"80%",overflow:"auto",position:"relative"},onClick:i=>i.stopPropagation(),children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",borderBottom:"1px solid #eee",paddingBottom:"10px"},children:[w.jsx("h3",{style:{margin:0},children:n}),w.jsx("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666"},children:"×"})]}),w.jsx("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.5",whiteSpace:"pre-wrap",wordBreak:"break-word"},children:r})]})}):null,a2=({onRun:e})=>{const[t,n]=K.useState(""),[r,i]=K.useState(""),[l,o]=K.useState(!1),[s,u]=K.useState(null),[a,c]=K.useState({isOpen:!1,title:"",content:""}),f=async()=>{if(!t.trim()){alert("Please enter the LGD transcript");return}if(!r.trim()){alert("Please enter the competency guidelines");return}try{o(!0);const x=await e({transcript:t.trim(),competencies:r.trim()});u(x)}catch{alert("Failed to run LGD evaluation")}finally{o(!1)}},p=(x,E)=>{c({isOpen:!0,title:x,content:E})},d=()=>{c({isOpen:!1,title:"",content:""})},h=async()=>{try{const x=await pp.getSampleTranscript();if(x.status!==200)throw new Error(`Failed to fetch transcript: ${x.status}`);const E=await x.data,m=await pp.getSampleCompetencies();if(m.status!==200)throw new Error(`Failed to fetch competencies: ${m.status}`);const g=await m.data;try{n(JSON.stringify(E,null,2))}catch{n(E.trim())}i(g.trim())}catch(x){console.error("Error loading sample data:",x),alert("Failed to load sample data from files: "+x.message)}};return w.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:"8px",padding:"20px",backgroundColor:"#f8f9fa"},children:[w.jsx("h3",{style:{marginBottom:"20px",color:"#495057"},children:"🎯 LGD Analysis Evaluation Runner"}),w.jsxs("div",{style:{marginBottom:"20px"},children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#495057"},children:"LGD Transcript (JSON format):"}),w.jsxs("div",{style:{display:"flex",gap:"10px",alignItems:"flex-start"},children:[w.jsx("textarea",{value:t,onChange:x=>n(x.target.value),placeholder:"Enter the LGD transcript in JSON format...",style:{flex:1,minHeight:"120px",padding:"12px",border:"1px solid #ced4da",borderRadius:"4px",fontSize:"14px",fontFamily:"monospace",resize:"vertical"}}),t&&w.jsx("button",{onClick:()=>p("LGD Transcript",t),style:{padding:"8px 12px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"12px",whiteSpace:"nowrap"},children:"View Full"})]})]}),w.jsxs("div",{style:{marginBottom:"20px"},children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#495057"},children:"Competency Guidelines:"}),w.jsxs("div",{style:{display:"flex",gap:"10px",alignItems:"flex-start"},children:[w.jsx("textarea",{value:r,onChange:x=>i(x.target.value),placeholder:"Enter the competency assessment guidelines...",style:{flex:1,minHeight:"120px",padding:"12px",border:"1px solid #ced4da",borderRadius:"4px",fontSize:"14px",resize:"vertical"}}),r&&w.jsx("button",{onClick:()=>p("Competency Guidelines",r),style:{padding:"8px 12px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"12px",whiteSpace:"nowrap"},children:"View Full"})]})]}),w.jsxs("div",{style:{display:"flex",gap:"10px",marginBottom:"20px"},children:[w.jsx("button",{onClick:f,disabled:l||!t.trim()||!r.trim(),style:{padding:"10px 20px",border:"none",borderRadius:"4px",backgroundColor:l?"#6c757d":"#28a745",color:"white",cursor:l?"not-allowed":"pointer",fontSize:"16px",fontWeight:"bold"},children:l?"Analyzing...":"Run LGD Analysis"}),w.jsx("button",{onClick:h,disabled:l,style:{padding:"10px 20px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"16px"},children:"Load Sample Data"})]}),l&&w.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"4px",color:"#856404"},children:[w.jsx("h4",{style:{margin:"0 0 10px 0",color:"#856404"},children:"⏳ Analysis in Progress"}),w.jsx("p",{style:{margin:0},children:"The LGD analysis is currently running. This process can take up to 10 minutes to complete. Please do not exit or refresh the page during this time."})]}),s&&w.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#d4edda",border:"1px solid #c3e6cb",borderRadius:"4px"},children:[w.jsx("h4",{style:{margin:"0 0 10px 0",color:"#155724"},children:"✅ Analysis Complete"}),w.jsx("p",{style:{margin:0,color:"#155724"},children:"LGD analysis completed successfully. Check the results table below for detailed output."})]}),w.jsx(u2,{isOpen:a.isOpen,onClose:d,title:a.title,content:a.content})]})},ig=({isOpen:e,onClose:t,promptData:n,promptName:r})=>e?w.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},onClick:t,children:w.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",padding:"24px",maxWidth:"800px",maxHeight:"80vh",width:"90%",overflow:"auto",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},onClick:i=>i.stopPropagation(),children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px",borderBottom:"1px solid #eee",paddingBottom:"16px"},children:[w.jsxs("h2",{style:{margin:0,color:"#333"},children:[r," - Version ",n==null?void 0:n.version]}),w.jsx("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666",padding:"0",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center"},children:"×"})]}),n?w.jsxs("div",{children:[w.jsxs("div",{style:{fontSize:"12px",color:"#666",marginBottom:"12px"},children:["Last updated: ",new Date(n.updatedAt).toLocaleString()]}),w.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"4px",padding:"16px",fontFamily:"monospace",fontSize:"14px",lineHeight:"1.5",whiteSpace:"pre",maxHeight:"400px",overflow:"auto",overflowX:"auto",width:"100%"},children:w.jsx(sm,{remarkPlugins:[Lm],children:n.content})})]}):w.jsx("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:"Loading prompt content..."}),w.jsx("div",{style:{marginTop:"20px",textAlign:"right"},children:w.jsx("button",{onClick:t,style:{padding:"8px 16px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Close"})})]})}):null,lg=({evaluationId:e,currentAnnotation:t,onAnnotationUpdate:n,disabled:r=!1})=>{const[i,l]=K.useState(!1),[o,s]=K.useState(!1),[u,a]=K.useState(!1),c=K.useRef(null);K.useEffect(()=>{if(!i||!c.current)return;const h=()=>{if(!c.current)return;const x=c.current.getBoundingClientRect(),E=window.innerHeight-x.bottom,m=x.top,g=120;E<g&&m>g?a(!0):a(!1)};return h(),window.addEventListener("scroll",h,!0),()=>{window.removeEventListener("scroll",h,!0)}},[i]);const f=async h=>{s(!0);try{await n(e,h),l(!1)}catch(x){console.error("Failed to update annotation:",x)}finally{s(!1)}},p=h=>{switch(h){case"Good":return"#28a745";case"Not Good":return"#dc3545";default:return"#6c757d"}},d=h=>{switch(h){case"Good":return"👍";case"Not Good":return"👎";default:return"📝"}};return w.jsxs("div",{style:{position:"relative",display:"inline-block"},ref:c,children:[t?w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[w.jsxs("span",{style:{padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500",backgroundColor:p(t)+"20",color:p(t),border:`1px solid ${p(t)}40`,display:"flex",alignItems:"center",gap:"4px"},children:[d(t)," ",t]}),!r&&w.jsx("button",{onClick:()=>l(!i),disabled:o,style:{padding:"2px 6px",border:"1px solid #6c757d",borderRadius:"4px",backgroundColor:"white",color:"#6c757d",cursor:o?"not-allowed":"pointer",fontSize:"10px",opacity:o?.6:1},children:o?"...":"Edit"})]}):w.jsx("button",{onClick:()=>l(!i),disabled:r||o,style:{padding:"4px 8px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:r||o?"not-allowed":"pointer",fontSize:"12px",opacity:r||o?.6:1},children:o?"Updating...":"📝 Annotate"}),i&&!r&&w.jsxs("div",{style:{position:"absolute",[u?"bottom":"top"]:"100%",left:0,zIndex:1e3,backgroundColor:"white",border:"1px solid #dee2e6",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)",minWidth:"120px",[u?"marginBottom":"marginTop"]:"4px"},children:[w.jsx("button",{onClick:()=>f("Good"),disabled:o,style:{width:"100%",padding:"8px 12px",border:"none",backgroundColor:"white",color:"#28a745",cursor:o?"not-allowed":"pointer",fontSize:"12px",textAlign:"left",display:"flex",alignItems:"center",gap:"6px",opacity:o?.6:1},onMouseEnter:h=>h.target.style.backgroundColor="#f8f9fa",onMouseLeave:h=>h.target.style.backgroundColor="white",children:"👍 Good"}),w.jsx("button",{onClick:()=>f("Not Good"),disabled:o,style:{width:"100%",padding:"8px 12px",border:"none",backgroundColor:"white",color:"#dc3545",cursor:o?"not-allowed":"pointer",fontSize:"12px",textAlign:"left",display:"flex",alignItems:"center",gap:"6px",opacity:o?.6:1},onMouseEnter:h=>h.target.style.backgroundColor="#f8f9fa",onMouseLeave:h=>h.target.style.backgroundColor="white",children:"👎 Not Good"}),t&&w.jsxs(w.Fragment,{children:[w.jsx("hr",{style:{margin:"4px 0",border:"none",borderTop:"1px solid #dee2e6"}}),w.jsx("button",{onClick:()=>f(null),disabled:o,style:{width:"100%",padding:"8px 12px",border:"none",backgroundColor:"white",color:"#6c757d",cursor:o?"not-allowed":"pointer",fontSize:"12px",textAlign:"left",display:"flex",alignItems:"center",gap:"6px",opacity:o?.6:1},onMouseEnter:h=>h.target.style.backgroundColor="#f8f9fa",onMouseLeave:h=>h.target.style.backgroundColor="white",children:"🗑️ Remove"})]})]}),i&&w.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:999},onClick:()=>l(!1)})]})},c2=e=>{try{const t=JSON.parse(e),n=[];return t.task_challenges&&t.task_challenges.length>0&&n.push({title:"Task Challenges:",items:t.task_challenges.map(r=>r.task_challenge||r)}),t.mentorships&&t.mentorships.length>0&&n.push({title:"Mentorships:",items:t.mentorships.map(r=>r.mentorship||r)}),t.training_workshops&&t.training_workshops.length>0&&n.push({title:"Training Workshops:",items:t.training_workshops.map(r=>r.training_workshop||r)}),n}catch{return null}},f2=({evaluations:e,onEvaluationUpdate:t})=>{const[n,r]=K.useState(null),[i,l]=K.useState({isOpen:!1,promptData:null,promptName:"",loading:!1}),o=c=>{r(n===c?null:c)},s=async(c,f,p,d=null)=>{l({isOpen:!0,promptData:null,promptName:p,loading:!0});try{if(d){const x=c===1?d.prompt1Content:d.prompt2Content;if(x){l(E=>({...E,promptData:{content:x,version:f,updatedAt:d.timestamp},loading:!1}));return}}const h=await Vl.getVersion(c,f);l(x=>({...x,promptData:h.data,loading:!1}))}catch(h){console.error("Error fetching prompt version:",h),l(x=>({...x,promptData:{content:"Error loading prompt content",version:f,updatedAt:new Date().toISOString()},loading:!1}))}},u=()=>{l({isOpen:!1,promptData:null,promptName:"",loading:!1})},a=async(c,f)=>{try{const p=await Su.updateAnnotation(c,f);t&&t(p.data)}catch(p){throw console.error("Failed to update annotation:",p),p}};return e.length===0?w.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:"8px",padding:"20px",backgroundColor:"white",textAlign:"center"},children:[w.jsx("h3",{children:"Evaluation Results"}),w.jsx("p",{style:{color:"#666"},children:"No evaluations run yet. Use the form above to run your first evaluation."})]}):w.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:"8px",backgroundColor:"white",overflow:"hidden"},children:[w.jsxs("h3",{style:{margin:0,padding:"16px",backgroundColor:"#f8f9fa",borderBottom:"1px solid #ddd"},children:["Evaluation Results (",e.length,")"]}),w.jsx("div",{style:{overflow:"auto"},children:w.jsxs("table",{style:{width:"100%",borderCollapse:"collapse",fontSize:"14px"},children:[w.jsx("thead",{children:w.jsxs("tr",{style:{backgroundColor:"#f8f9fa"},children:[w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"1px solid #ddd"},children:"Timestamp"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"1px solid #ddd"},children:"Prompt Versions"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"1px solid #ddd"},children:"Input"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"1px solid #ddd"},children:"Objectives"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"1px solid #ddd"},children:"Output"}),w.jsx("th",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #ddd"},children:"Annotation"}),w.jsx("th",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #ddd"},children:"Actions"})]})}),w.jsx("tbody",{children:e.map(c=>w.jsxs(_u.Fragment,{children:[w.jsxs("tr",{style:{borderBottom:"1px solid #eee",backgroundColor:n===c.id?"#f8f9fa":"white"},children:[w.jsx("td",{style:{padding:"12px",verticalAlign:"top"},children:new Date(c.timestamp).toLocaleString()}),w.jsxs("td",{style:{padding:"12px",verticalAlign:"top"},children:[w.jsxs("div",{children:["P1: ",w.jsxs("button",{onClick:()=>s(1,c.prompt1Version,"Prompt 1",c),style:{background:"none",border:"none",color:"#007bff",textDecoration:"underline",cursor:"pointer",padding:0,font:"inherit"},children:["v",c.prompt1Version]})]}),w.jsxs("div",{children:["P2: ",w.jsxs("button",{onClick:()=>s(2,c.prompt2Version,"Prompt 2",c),style:{background:"none",border:"none",color:"#007bff",textDecoration:"underline",cursor:"pointer",padding:0,font:"inherit"},children:["v",c.prompt2Version]})]})]}),w.jsx("td",{style:{padding:"12px",verticalAlign:"top",maxWidth:"200px"},children:w.jsx("div",{style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:c.formattedInput||c.input})}),w.jsx("td",{style:{padding:"12px",verticalAlign:"top",maxWidth:"200px"},children:w.jsx("div",{style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:c.objectives&&c.objectives.length>0?`${c.objectives.length} objective${c.objectives.length>1?"s":""} generated`:"No objectives"})}),w.jsx("td",{style:{padding:"12px",verticalAlign:"top",maxWidth:"300px"},children:w.jsx("div",{style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:c.output})}),w.jsx("td",{style:{padding:"12px",textAlign:"center",verticalAlign:"top"},children:w.jsx(lg,{evaluationId:c.id,currentAnnotation:c.annotation,onAnnotationUpdate:a})}),w.jsx("td",{style:{padding:"12px",textAlign:"center",verticalAlign:"top"},children:w.jsx("button",{onClick:()=>o(c.id),style:{padding:"4px 8px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"12px"},children:n===c.id?"Collapse":"Expand"})})]}),n===c.id&&w.jsx("tr",{children:w.jsx("td",{colSpan:"7",style:{padding:"16px",backgroundColor:"#f8f9fa",borderBottom:"1px solid #ddd"},children:w.jsxs("div",{style:{display:"grid",gap:"16px"},children:[w.jsxs("div",{children:[w.jsx("h4",{style:{margin:"0 0 8px 0"},children:"Input:"}),w.jsx("div",{style:{padding:"8px",backgroundColor:"white",border:"1px solid #ddd",borderRadius:"4px",fontFamily:"monospace",fontSize:"13px",whiteSpace:"pre-wrap"},children:c.formattedInput||c.input})]}),c.objectives&&c.objectives.length>0&&w.jsxs("div",{children:[w.jsx("h4",{style:{margin:"0 0 8px 0"},children:"Generated Objectives:"}),w.jsx("div",{style:{padding:"8px",backgroundColor:"white",border:"1px solid #ddd",borderRadius:"4px",fontFamily:"monospace",fontSize:"13px",whiteSpace:"pre-wrap"},children:c.objectives.map((f,p)=>`${p+1}. ${f}`).join(`
`)})]}),w.jsxs("div",{children:[w.jsx("h4",{style:{margin:"0 0 8px 0"},children:"Final Output:"}),w.jsx("div",{style:{padding:"8px",backgroundColor:"white",border:"1px solid #ddd",borderRadius:"4px",fontSize:"13px"},children:(()=>{const f=c2(c.output);return f?f.length===0?w.jsx("div",{style:{color:"#666",fontStyle:"italic"},children:"No recommendations generated"}):w.jsx("div",{children:f.map((p,d)=>w.jsxs("div",{style:{marginBottom:d<f.length-1?"12px":"0"},children:[w.jsx("div",{style:{fontWeight:"bold",marginBottom:"4px",color:"#333"},children:p.title}),w.jsx("ul",{style:{margin:"0",paddingLeft:"20px",listStyleType:"disc"},children:p.items.map((h,x)=>w.jsx("li",{style:{marginBottom:"4px",lineHeight:"1.4"},children:h},x))})]},d))}):w.jsx("div",{style:{fontFamily:"monospace",whiteSpace:"pre-wrap"},children:c.output})})()})]})]})})})]},c.id))})]})}),w.jsx(ig,{isOpen:i.isOpen,onClose:u,promptData:i.promptData,promptName:i.promptName})]})},p2=({isOpen:e,onClose:t,title:n,content:r})=>e?w.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e4},onClick:t,children:w.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",padding:"20px",maxWidth:"80%",maxHeight:"80%",overflow:"auto",position:"relative"},onClick:i=>i.stopPropagation(),children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",borderBottom:"1px solid #eee",paddingBottom:"10px"},children:[w.jsx("h3",{style:{margin:0},children:n}),w.jsx("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666"},children:"×"})]}),w.jsx("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.5",whiteSpace:"pre-wrap",wordBreak:"break-word"},children:r})]})}):null,d2=({evaluations:e,onEvaluationUpdate:t})=>{const[n,r]=K.useState(null),[i,l]=K.useState({isOpen:!1,promptData:null,promptName:"",loading:!1}),[o,s]=K.useState({isOpen:!1,title:"",content:""}),[u,a]=K.useState({}),c=v=>{r(n===v?null:v)},f=()=>{l({isOpen:!1,promptData:null,promptName:"",loading:!1})},p=(v,T)=>{s({isOpen:!0,title:v,content:T})},d=()=>{s({isOpen:!1,title:"",content:""})},h=async(v,T)=>{try{const C=await Cu.updateAnnotation(v,T);t&&t(C.data)}catch(C){throw console.error("Failed to update annotation:",C),C}},x=async(v,T,C)=>{try{const _=y(T,C);await navigator.clipboard.writeText(_),a(A=>({...A,[v]:"success"})),setTimeout(()=>{a(A=>({...A,[v]:null}))},2e3)}catch(_){console.error("Failed to copy to clipboard:",_),a(A=>({...A,[v]:"error"})),setTimeout(()=>{a(A=>({...A,[v]:null}))},2e3)}},E=(v,T=100)=>v?v.length>T?v.substring(0,T)+"...":v:"No content available",m=v=>{try{let T="No transcript available",C="No competencies available",_={};if(v.input&&typeof v.input=="object")T=v.input.transcript||"No transcript available",C=v.input.competencies||"No competencies available";else if(v.formattedInput){const A=v.formattedInput.split(`
`),N=A.find(j=>j.startsWith("Transcript:")),b=A.find(j=>j.startsWith("Competencies:"));T=N?N.replace("Transcript:","").trim():"No transcript available",C=b?b.replace("Competencies:","").trim():"No competencies available"}if(T!=="No transcript available"&&T.startsWith("["))try{const A=JSON.parse(T);Array.isArray(A)&&A.length>0&&A.forEach(N=>{_[N.user_id]||(_[N.user_id]=N.user_name)})}catch(A){console.warn("Could not parse transcript as JSON:",A)}return{transcript:T,competencies:C,userMapping:_}}catch(T){return console.error("Error parsing input data:",T),{transcript:"Error parsing transcript",competencies:"Error parsing competencies",userName:"Error"}}},g=async(v,T,C,_=null)=>{l({isOpen:!0,promptData:null,promptName:C,loading:!0});try{if(_){const N=v===3?_.prompt1Content:_.prompt2Content;if(N){l(b=>({...b,promptData:{content:N,version:T,updatedAt:_.timestamp},loading:!1}));return}}const A=await Vl.getVersion(v,T);l(N=>({...N,promptData:A.data,loading:!1}))}catch(A){console.error("Error fetching prompt version:",A),l(N=>({...N,loading:!1}))}},y=(v,T)=>{let C;if(typeof v=="string")try{C=JSON.parse(v)}catch{return v}else if(typeof v=="object"&&v!==null)C=v;else return JSON.stringify(v,null,2);const A=m(T).userMapping;if(C&&C.result&&Array.isArray(C.result)){const N=C.result.map(b=>{const j=b.detail_scores.map(W=>{const ee=W.aspect_details.map(V=>({level:V.level,key_behaviours:[{name:V.name,evidences:V.evidences.map(ae=>({evidence:ae.evidence,description:ae.description,timestamp:ae.timestamp}))}]}));return{name:W.competency_name,score:W.average_score,details:ee}}),F=A[b.user_id]||b.user_id;return{user_id:b.user_id,user_name:F,competencies:j}});return JSON.stringify(N,null,2)}return JSON.stringify(C,null,2)};return!e||e.length===0?w.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#6c757d",backgroundColor:"#f8f9fa",borderRadius:"8px",border:"1px solid #dee2e6"},children:[w.jsx("h3",{children:"No LGD Analysis Results"}),w.jsx("p",{children:"Run an LGD analysis to see results here."})]}):w.jsxs("div",{children:[w.jsxs("h2",{style:{marginBottom:"20px",color:"#495057"},children:["📊 LGD Analysis Results (",e.length,")"]}),w.jsx("div",{style:{border:"1px solid #dee2e6",borderRadius:"8px",overflow:"hidden",backgroundColor:"white"},children:w.jsxs("table",{style:{width:"100%",borderCollapse:"collapse"},children:[w.jsx("thead",{style:{backgroundColor:"#f8f9fa"},children:w.jsxs("tr",{children:[w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #dee2e6"},children:"Timestamp"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #dee2e6"},children:"Prompt Versions"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #dee2e6"},children:"Input Summary"}),w.jsx("th",{style:{padding:"12px",textAlign:"center",borderBottom:"2px solid #dee2e6"},children:"Annotation"}),w.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #dee2e6"},children:"Actions"})]})}),w.jsx("tbody",{children:e.map(v=>w.jsxs(_u.Fragment,{children:[w.jsxs("tr",{style:{borderBottom:"1px solid #eee",backgroundColor:n===v.id?"#f8f9fa":"white"},children:[w.jsx("td",{style:{padding:"12px",verticalAlign:"top"},children:new Date(v.timestamp).toLocaleString()}),w.jsxs("td",{style:{padding:"12px",verticalAlign:"top"},children:[w.jsxs("div",{children:["P3: ",w.jsxs("button",{onClick:()=>g(3,v.prompt1Version,"LGD Analysis Prompt",v),style:{background:"none",border:"none",color:"#007bff",textDecoration:"underline",cursor:"pointer",padding:0,font:"inherit"},children:["v",v.prompt1Version]})]}),w.jsxs("div",{children:["P4: ",w.jsxs("button",{onClick:()=>g(4,v.prompt2Version,"LGD Formatting Prompt",v),style:{background:"none",border:"none",color:"#007bff",textDecoration:"underline",cursor:"pointer",padding:0,font:"inherit"},children:["v",v.prompt2Version]})]})]}),w.jsx("td",{style:{padding:"12px",verticalAlign:"top"},children:(()=>{const T=m(v);return w.jsxs("div",{style:{fontSize:"12px",color:"#495057"},children:[w.jsxs("div",{style:{marginBottom:"4px"},children:["• ",w.jsx("strong",{children:"Transcript:"})," ",E(T.transcript,50)]}),w.jsxs("div",{children:["• ",w.jsx("strong",{children:"Competencies:"})," ",E(T.competencies,50)]})]})})()}),w.jsx("td",{style:{padding:"12px",textAlign:"center",verticalAlign:"top"},children:w.jsx(lg,{evaluationId:v.id,currentAnnotation:v.annotation,onAnnotationUpdate:h})}),w.jsx("td",{style:{padding:"12px",verticalAlign:"top"},children:w.jsxs("button",{onClick:()=>c(v.id),style:{padding:"6px 12px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:n===v.id?"#007bff":"white",color:n===v.id?"white":"#007bff",cursor:"pointer",fontSize:"12px"},children:[n===v.id?"Hide":"Show"," Details"]})})]}),n===v.id&&w.jsx("tr",{children:w.jsx("td",{colSpan:"5",style:{padding:"20px",backgroundColor:"#f8f9fa"},children:w.jsxs("div",{style:{display:"grid",gap:"20px"},children:[w.jsxs("div",{children:[w.jsx("h4",{style:{marginBottom:"10px",color:"#495057"},children:"📝 Input Data"}),w.jsx("div",{style:{backgroundColor:"white",padding:"15px",borderRadius:"4px",border:"1px solid #dee2e6"},children:(()=>{const T=m(v);return w.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[w.jsxs("span",{style:{fontSize:"14px",color:"#495057"},children:["• ",w.jsx("strong",{children:"Transcript:"})," ",E(T.transcript,80)]}),w.jsx("button",{onClick:()=>p("LGD Transcript",T.transcript),style:{padding:"4px 8px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"11px",whiteSpace:"nowrap"},children:"View Full"})]}),w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[w.jsxs("span",{style:{fontSize:"14px",color:"#495057"},children:["• ",w.jsx("strong",{children:"Competencies:"})," ",E(T.competencies,80)]}),w.jsx("button",{onClick:()=>p("Competency Guidelines",T.competencies),style:{padding:"4px 8px",border:"1px solid #007bff",borderRadius:"4px",backgroundColor:"white",color:"#007bff",cursor:"pointer",fontSize:"11px",whiteSpace:"nowrap"},children:"View Full"})]})]})})()})]}),w.jsxs("div",{children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"10px"},children:[w.jsx("h4",{style:{margin:0,color:"#495057"},children:"🎯 Analysis Output"}),w.jsx("button",{onClick:()=>x(v.id,v.output,v),style:{padding:"4px 8px",border:"1px solid #28a745",borderRadius:"4px",backgroundColor:u[v.id]==="success"?"#28a745":u[v.id]==="error"?"#dc3545":"white",color:u[v.id]==="success"||u[v.id]==="error"?"white":"#28a745",cursor:"pointer",fontSize:"11px",whiteSpace:"nowrap",display:"flex",alignItems:"center",gap:"4px",transition:"all 0.2s ease"},title:"Copy analysis output to clipboard",children:u[v.id]==="success"?"✓ Copied!":u[v.id]==="error"?"✗ Error":"📋 Copy"})]}),w.jsx("pre",{style:{backgroundColor:"white",padding:"15px",borderRadius:"4px",border:"1px solid #dee2e6",fontSize:"12px",overflow:"auto",maxHeight:"400px",whiteSpace:"pre-wrap",wordBreak:"break-word"},children:y(v.output,v)})]})]})})})]},v.id))})]})}),w.jsx(ig,{isOpen:i.isOpen,onClose:f,promptData:i.promptData,promptName:i.promptName,loading:i.loading}),w.jsx(p2,{isOpen:o.isOpen,onClose:d,title:o.title,content:o.content})]})},h2=({activeEvalType:e,onEvalTypeChange:t})=>{const n={width:"250px",backgroundColor:"#f8f9fa",borderRight:"1px solid #dee2e6",padding:"20px",height:"100vh",position:"sticky",top:0},r={padding:"12px 16px",margin:"8px 0",borderRadius:"6px",cursor:"pointer",transition:"all 0.2s ease",fontWeight:"500",border:"none",width:"100%",textAlign:"left",fontSize:"14px"},i={...r,backgroundColor:"#007bff",color:"white"},l={...r,backgroundColor:"white",color:"#495057",border:"1px solid #dee2e6"};return w.jsxs("div",{style:n,children:[w.jsx("h3",{style:{marginBottom:"24px",color:"#495057",fontSize:"18px",fontWeight:"600"},children:"Evaluation Types"}),w.jsxs("div",{style:{display:"flex",flexDirection:"column"},children:[w.jsx("button",{style:e==="idp"?i:l,onClick:()=>t("idp"),onMouseEnter:o=>{e!=="idp"&&(o.target.style.backgroundColor="#e9ecef")},onMouseLeave:o=>{e!=="idp"&&(o.target.style.backgroundColor="white")},children:"📋 IDP Recommendation"}),w.jsx("button",{style:e==="lgd"?i:l,onClick:()=>t("lgd"),onMouseEnter:o=>{e!=="lgd"&&(o.target.style.backgroundColor="#e9ecef")},onMouseLeave:o=>{e!=="lgd"&&(o.target.style.backgroundColor="white")},children:"👥 LGD Analysis"})]}),w.jsxs("div",{style:{marginTop:"32px",padding:"16px",backgroundColor:"#e3f2fd",borderRadius:"6px",fontSize:"12px",color:"#1565c0"},children:[w.jsx("strong",{children:"Current:"}),w.jsx("br",{}),e==="idp"?"IDP Recommendation Evaluations":"LGD Analysis Evaluations"]})]})},m2=()=>{const[e,t]=K.useState([]),[n,r]=K.useState([]),[i,l]=K.useState([]),[o,s]=K.useState(!0),[u,a]=K.useState(null),[c,f]=K.useState("idp");K.useEffect(()=>{p()},[]);const p=async()=>{try{s(!0);const[y,v]=await Promise.all([Vl.getAll(),Su.getAll()]);let T={data:[]};try{T=await Cu.getAll()}catch(C){console.warn("LGD evaluations not available yet:",C)}t(y.data),r(v.data),l(T.data),a(null)}catch(y){a("Failed to load data"),console.error("Error loading data:",y)}finally{s(!1)}},d=async(y,v)=>{try{const T=await Vl.update(y,v);return t(C=>C.map(_=>_.id===y?T.data:_)),T.data}catch(T){throw console.error("Error updating prompt:",T),T}},h=async y=>{try{const v=await Su.run(y);return r(T=>[v.data,...T]),v.data}catch(v){throw console.error("Error running evaluation:",v),v}},x=async y=>{try{const v=await Cu.run(y);return l(T=>[v.data,...T]),v.data}catch(v){throw console.error("Error running LGD evaluation:",v),v}},E=y=>{r(v=>v.map(T=>T.id===y.id?y:T))},m=y=>{l(v=>v.map(T=>T.id===y.id?y:T))};if(o)return w.jsx("div",{style:{padding:"20px",textAlign:"center"},children:w.jsx("h2",{children:"Loading..."})});if(u)return w.jsxs("div",{style:{padding:"20px",textAlign:"center",color:"red"},children:[w.jsxs("h2",{children:["Error: ",u]}),w.jsx("button",{onClick:p,children:"Retry"})]});const g=()=>c==="idp"?e.filter(y=>y.id<=2):e.filter(y=>y.id===3);return w.jsxs("div",{style:{display:"flex",minHeight:"100vh"},children:[w.jsx(h2,{activeEvalType:c,onEvalTypeChange:f}),w.jsxs("div",{style:{flex:1,padding:"20px",maxWidth:"1400px",margin:"0 auto"},children:[w.jsxs("h1",{style:{textAlign:"center",marginBottom:"30px"},children:[c==="idp"?"IDP Recommendation":"LGD Analysis"," Prompt Evaluations"]}),w.jsxs("div",{style:{display:"grid",gap:"20px",marginBottom:"30px"},children:[w.jsx("div",{className:"prompt-grid",style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(400px, 1fr))",gap:"20px"},children:g().map(y=>w.jsx(_C,{prompt:y,onUpdate:d},y.id))}),c==="idp"?w.jsx(LC,{onRun:h}):w.jsx(a2,{onRun:x})]}),c==="idp"?w.jsx(f2,{evaluations:n,onEvaluationUpdate:E}):w.jsx(d2,{evaluations:i,onEvaluationUpdate:m})]})]})};fs.createRoot(document.getElementById("root")).render(w.jsx(_u.StrictMode,{children:w.jsx(m2,{})}));
